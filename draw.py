import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']  # 使用黑体或微软雅黑
rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 2022年基层法律服务工作者数据
# 原始数据（单位：万件）
data_1 = {
    '诉讼案件': 63.4,
    '非诉讼法律事务': 13.0,
    '法律顾问服务': 6.3,
    '参与仲裁': 5.0
}

# 公益法律服务数据（单位：万件）
data_2 = {
    '法律援助案件': 12.2,
    '人民调解': 18.6,
    '信访案件': 4.3
}

# 其他服务数据（单位：万）
data_3 = {
    '村居法律顾问': 12.6,
    '弱势群体免费服务': 45.1
}

def create_bar_chart():
    """创建柱状图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 图1：基本法律服务柱状图
    categories_1 = list(data_1.keys())
    values_1 = list(data_1.values())
    colors_1 = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    bars1 = ax1.bar(categories_1, values_1, color=colors_1, alpha=0.8)
    ax1.set_title('2022年基层法律服务工作者基本服务情况', fontsize=14, fontweight='bold')
    ax1.set_ylabel('数量（万件/万家）', fontsize=12)
    ax1.set_ylim(0, max(values_1) * 1.2)

    # 在柱子上添加数值标签
    for bar, value in zip(bars1, values_1):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}万', ha='center', va='bottom', fontsize=10)

    # 图2：公益法律服务柱状图
    categories_2 = list(data_2.keys())
    values_2 = list(data_2.values())
    colors_2 = ['#FFD93D', '#6BCF7F', '#FF8C42']

    bars2 = ax2.bar(categories_2, values_2, color=colors_2, alpha=0.8)
    ax2.set_title('2022年公益法律服务情况', fontsize=14, fontweight='bold')
    ax2.set_ylabel('数量（万件）', fontsize=12)
    ax2.set_ylim(0, max(values_2) * 1.2)

    for bar, value in zip(bars2, values_2):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{value}万', ha='center', va='bottom', fontsize=10)

    # 图3：其他服务柱状图
    categories_3 = list(data_3.keys())
    values_3 = list(data_3.values())
    colors_3 = ['#A8E6CF', '#FFB3BA']

    bars3 = ax3.bar(categories_3, values_3, color=colors_3, alpha=0.8)
    ax3.set_title('2022年其他法律服务情况', fontsize=14, fontweight='bold')
    ax3.set_ylabel('数量（万个/万件）', fontsize=12)
    ax3.set_ylim(0, max(values_3) * 1.2)

    for bar, value in zip(bars3, values_3):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}万', ha='center', va='bottom', fontsize=10)

    # 图4：总体对比柱状图
    all_categories = ['诉讼案件', '非诉讼事务', '人民调解', '弱势群体服务', '法律援助', '法律顾问']
    all_values = [63.4, 13.0, 18.6, 45.1, 12.2, 6.3]
    colors_4 = plt.cm.Set3(np.linspace(0, 1, len(all_categories)))

    bars4 = ax4.bar(all_categories, all_values, color=colors_4, alpha=0.8)
    ax4.set_title('2022年主要法律服务项目对比', fontsize=14, fontweight='bold')
    ax4.set_ylabel('数量（万件/万家）', fontsize=12)
    ax4.set_ylim(0, max(all_values) * 1.2)
    plt.setp(ax4.get_xticklabels(), rotation=45, ha='right')

    for bar, value in zip(bars4, all_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}万', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    plt.savefig('基层法律服务柱状图.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_pie_chart():
    """创建饼图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 饼图1：基本法律服务分布
    labels_1 = list(data_1.keys())
    sizes_1 = list(data_1.values())
    colors_1 = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    explode_1 = (0.1, 0, 0, 0)  # 突出显示诉讼案件

    wedges1, texts1, autotexts1 = ax1.pie(sizes_1, labels=labels_1, colors=colors_1,
                                          autopct='%1.1f%%', startangle=90, explode=explode_1)
    ax1.set_title('基本法律服务分布', fontsize=14, fontweight='bold')

    # 饼图2：公益法律服务分布
    labels_2 = list(data_2.keys())
    sizes_2 = list(data_2.values())
    colors_2 = ['#FFD93D', '#6BCF7F', '#FF8C42']
    explode_2 = (0, 0.1, 0)  # 突出显示人民调解

    wedges2, texts2, autotexts2 = ax2.pie(sizes_2, labels=labels_2, colors=colors_2,
                                          autopct='%1.1f%%', startangle=90, explode=explode_2)
    ax2.set_title('公益法律服务分布', fontsize=14, fontweight='bold')

    # 饼图3：其他服务分布
    labels_3 = list(data_3.keys())
    sizes_3 = list(data_3.values())
    colors_3 = ['#A8E6CF', '#FFB3BA']
    explode_3 = (0, 0.1)  # 突出显示弱势群体服务

    wedges3, texts3, autotexts3 = ax3.pie(sizes_3, labels=labels_3, colors=colors_3,
                                          autopct='%1.1f%%', startangle=90, explode=explode_3)
    ax3.set_title('其他法律服务分布', fontsize=14, fontweight='bold')

    # 饼图4：总体服务分布
    all_labels = ['诉讼案件', '非诉讼事务', '人民调解', '弱势群体服务', '法律援助', '其他']
    all_sizes = [63.4, 13.0, 18.6, 45.1, 12.2, 11.3]  # 其他包含法律顾问和信访等
    colors_4 = plt.cm.Set3(np.linspace(0, 1, len(all_labels)))
    explode_4 = (0.1, 0, 0, 0.05, 0, 0)  # 突出显示诉讼案件和弱势群体服务

    wedges4, texts4, autotexts4 = ax4.pie(all_sizes, labels=all_labels, colors=colors_4,
                                          autopct='%1.1f%%', startangle=90, explode=explode_4)
    ax4.set_title('2022年法律服务总体分布', fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.savefig('基层法律服务饼图.png', dpi=300, bbox_inches='tight')
    plt.close()

def print_data_summary():
    """打印数据摘要"""
    print("=" * 60)
    print("2022年全国基层法律服务工作者数据摘要")
    print("=" * 60)

    print("\n📊 基本法律服务：")
    for key, value in data_1.items():
        print(f"  • {key}: {value}万件/万家")

    print("\n🤝 公益法律服务：")
    total_public = sum(data_2.values())
    print(f"  • 总计: {total_public}万件")
    for key, value in data_2.items():
        percentage = (value / total_public) * 100
        print(f"  • {key}: {value}万件 ({percentage:.1f}%)")

    print("\n🏘️ 其他服务：")
    for key, value in data_3.items():
        print(f"  • {key}: {value}万个/万件")

    print("\n📈 主要数据亮点：")
    print(f"  • 诉讼案件处理量最大: {data_1['诉讼案件']}万件")
    print(f"  • 弱势群体免费服务: {data_3['弱势群体免费服务']}万件")
    print(f"  • 人民调解参与: {data_2['人民调解']}万件")
    print("=" * 60)

if __name__ == "__main__":
    # 打印数据摘要
    print_data_summary()

    # 创建图表
    print("\n正在生成柱状图...")
    create_bar_chart()

    print("\n正在生成饼图...")
    create_pie_chart()

    print("\n✅ 图表生成完成！")
    print("📁 已保存文件：")
    print("  • 基层法律服务柱状图.png")
    print("  • 基层法律服务饼图.png")