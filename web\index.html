<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法弈 - 智能法律诉讼辅助系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
            min-height: 100vh;
            color: #333333;
            overflow-x: hidden;
        }

        /* 添加背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(179, 205, 224, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(162, 217, 206, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(245, 166, 35, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 页面切换 */
        .page {
            display: none;
            min-height: 100vh;
            animation: fadeIn 0.8s ease;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 欢迎页样式 */
        .welcome-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
            text-align: center;
        }

        .logo-text {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .tagline {
            font-size: 1.2rem;
            color: #666;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 4rem;
        }

        /* 功能网格 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(179, 205, 224, 0.3);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        /* 为不同功能卡片设置不同的图标颜色 */
        .feature-card:nth-child(1) .feature-icon {
            color: #B3CDE0; /* 案件智能解构 - 蓝色 */
        }

        .feature-card:nth-child(2) .feature-icon {
            color: #A2D9CE; /* 证据链诊断 - 绿色 */
        }

        .feature-card:nth-child(3) .feature-icon {
            color: #F5A623; /* 法律与案例指引 - 橙色 */
        }

        .feature-card:nth-child(4) .feature-icon {
            color: #9b59b6; /* 诉讼文书生成 - 紫色 */
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }

        /* 按钮样式 */
        .start-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 3rem;
            font-size: 1.2rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 体验页样式 */
        .experience-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 3rem;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .input-group {
            position: relative;
        }

        .input-group label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .input-group textarea {
            width: 100%;
            min-height: 200px;
            padding: 1.5rem;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-family: inherit;
            font-size: 1.1rem;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #B3CDE0;
            box-shadow: 0 0 0 3px rgba(179, 205, 224, 0.1);
        }

        .example-btn {
            display: none;
        }

        .analyze-section {
            text-align: center;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(179, 205, 224, 0.3);
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(179, 205, 224, 0.4);
        }

        /* 分析过程展示区 */
        .analysis-section {
            margin: 4rem 0;
            border-radius: 20px;
            overflow: hidden;
        }

        .analysis-background {
            position: relative;
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(162, 217, 206, 0.1) 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #networkCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.6;
        }

        .analysis-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .analysis-text h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .analysis-stage {
            font-size: 1rem;
            color: #666;
            margin-bottom: 1.5rem;
            font-weight: 400;
            transition: color 0.3s ease;
        }

        .analysis-stage.completed {
            color: #28a745;
            font-weight: 500;
        }

        /* 进度条样式 */
        .progress-container {
            width: 100%;
            max-width: 400px;
            margin: 1.5rem auto;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(179, 205, 224, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #B3CDE0 0%, #A2D9CE 50%, #B3CDE0 100%);
            border-radius: 12px;
            width: 0%;
            transition: width 0.5s ease-out;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(179, 205, 224, 0.3);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .progress-dots {
            display: inline-flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .progress-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #B3CDE0;
            animation: pulse 1.5s infinite;
        }

        .progress-dots span:nth-child(2) {
            animation-delay: 0.3s;
        }

        .progress-dots span:nth-child(3) {
            animation-delay: 0.6s;
        }

        /* 消息加载动画 */
        .loading-dots {
            display: inline-flex;
            gap: 0.3rem;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 0;
        }

        .loading-dots span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #666;
            animation: messagePulse 1.2s infinite;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        @keyframes messagePulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.3); }
        }

        .view-report-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .view-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 结果展示区 */
        .results-section {
            margin-top: 4rem;
        }

        .evidence-chain {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-chain h3 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 500;
        }

        .evidence-timeline {
            position: relative;
            padding: 2rem 0;
            min-height: 800px;
        }

        .evidence-container {
            display: grid;
            grid-template-columns: 280px 1fr 280px;
            gap: 1.5rem;
            height: 800px;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .evidence-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-list h4 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .evidence-list-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #B3CDE0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .evidence-list-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(179, 205, 224, 0.3);
        }

        .evidence-list-item.evidence_owned {
            border-left-color: #A2D9CE;
        }

        .evidence-list-item.evidence_owned::before {
            content: '■';
            color: #A2D9CE;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.evidence_gap {
            border-left-color: #ff6b6b;
        }

        .evidence-list-item.evidence_gap::before {
            content: '●';
            color: #ff6b6b;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.conclusion {
            border-left-color: #F5A623;
        }

        .evidence-list-item.conclusion::before {
            content: '♦';
            color: #F5A623;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.final_claim {
            border-left-color: #9b59b6;
        }

        .evidence-list-item.final_claim::before {
            content: '▲';
            color: #9b59b6;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item h5 {
            color: #333;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .evidence-list-item p {
            color: #666;
            font-size: 0.8rem;
            line-height: 1.4;
            margin: 0;
        }

        .evidence-graph {
            position: relative;
            width: 100%;
            height: 800px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 16px;
            overflow: visible;
            padding: 20px;
            box-sizing: border-box;
        }

        .evidence-report {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-report h4 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .report-section {
            margin-bottom: 1.5rem;
        }

        .report-section h5 {
            color: #B3CDE0;
            font-size: 1rem;
            margin-bottom: 0.8rem;
            font-weight: 500;
        }

        .report-section p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 0.8rem;
        }

        .report-highlight {
            background: rgba(245, 166, 35, 0.1);
            border-left: 3px solid #F5A623;
            padding: 0.8rem;
            border-radius: 6px;
            margin: 0.5rem 0;
        }

        .evidence-node {
            position: absolute;
            background: white;
            border-radius: 16px;
            padding: 1rem;
            width: 140px;
            height: 90px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 3px solid #B3CDE0;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            animation: nodeAppear 0.8s ease forwards;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .evidence-node:hover {
            z-index: 10;
        }

        .evidence-node.evidence_owned {
            border-color: #A2D9CE;
            background: rgba(162, 217, 206, 0.1);
            border-radius: 8px; /* 圆角矩形 - 已有证据 */
            border-width: 4px;
            border-style: solid;
        }

        .evidence-node.evidence_owned:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(162, 217, 206, 0.4);
        }

        .evidence-node.evidence_gap {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 50%; /* 圆形 - 证据缺口 */
            border-style: dashed;
            border-width: 4px;
        }

        .evidence-node.evidence_gap:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .evidence-node.conclusion {
            border-color: #F5A623;
            background: rgba(245, 166, 35, 0.1);
            border-radius: 50%; /* 圆形 - 结论节点 */
            border-width: 4px;
            border-style: solid;
            width: 140px; /* 增大尺寸并确保正圆形 */
            height: 140px;
            min-width: 140px; /* 防止内容挤压变形 */
            min-height: 140px;
        }

        .evidence-node.conclusion:hover {
            transform: scale(1.05); /* 移除旋转，只保持放大 */
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.4);
        }

        .evidence-node.final_claim {
            border-color: #9b59b6;
            background: rgba(155, 89, 182, 0.1);
            border-radius: 50%; /* 圆形 - 最终诉求 */
            border-width: 4px;
            border-style: solid;
            width: 140px; /* 增大尺寸并确保正圆形 */
            height: 140px;
            min-width: 140px; /* 防止内容挤压变形 */
            min-height: 140px;
            position: relative;
        }

        .evidence-node.final_claim:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
        }

        .evidence-node.evidence_gap::before {
            content: '⚠️';
            position: absolute;
            top: 0.3rem;
            right: 0.3rem;
            font-size: 0.9rem;
        }

        @keyframes nodeAppear {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .evidence-node h4 {
            color: #333;
            margin: 0;
            font-weight: 500;
            font-size: 0.85rem;
            line-height: 1.2;
            text-align: center;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        /* 确保所有节点类型的文字样式统一 */
        .evidence-node.evidence_owned h4,
        .evidence-node.evidence_gap h4,
        .evidence-node.conclusion h4,
        .evidence-node.final_claim h4 {
            color: #333;
            margin: 0;
            font-weight: 500;
            font-size: 0.85rem;
            line-height: 1.2;
            text-align: center;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .evidence-connection {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .evidence-connection svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .evidence-connection line {
            stroke: #B3CDE0;
            stroke-width: 3;
            opacity: 0;
            animation: lineAppear 1s ease forwards;
            marker-end: url(#arrowhead);
        }

        .evidence-connection.gap line {
            stroke: #ff6b6b;
            stroke-width: 3;
            stroke-dasharray: 8,4;
            marker-end: url(#arrowhead-gap);
        }

        @keyframes lineAppear {
            to {
                opacity: 0.8;
            }
        }

        /* 箭头标记 */
        .arrow-marker {
            fill: #B3CDE0;
        }

        .arrow-marker-gap {
            fill: #ff6b6b;
        }

        .full-report-btn {
            display: block;
            margin: 2rem auto 0;
            background: linear-gradient(135deg, #A2D9CE 0%, #B3CDE0 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(162, 217, 206, 0.3);
        }

        .full-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(162, 217, 206, 0.4);
        }

        /* 综合报告 */
        .full-report {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .report-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 2rem;
            gap: 1rem;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            color: #B3CDE0;
            background: rgba(179, 205, 224, 0.1);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #B3CDE0;
        }

        .tab-panel {
            display: none;
            animation: fadeIn 0.5s ease;
            min-height: 400px;
        }

        .tab-panel.active {
            display: block;
        }

        .tab-panel.loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }

        .loading-content {
            text-align: center;
            color: #666;
        }

        .loading-spinner {
            display: inline-flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .loading-spinner span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #B3CDE0;
            animation: pulse 1.5s infinite;
        }

        .loading-spinner span:nth-child(2) {
            animation-delay: 0.3s;
        }

        .loading-spinner span:nth-child(3) {
            animation-delay: 0.6s;
        }

        .report-content {
            line-height: 1.8;
            color: #333;
        }

        .report-content h4 {
            color: #B3CDE0;
            font-size: 1.3rem;
            margin: 2rem 0 1rem 0;
            font-weight: 500;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .statute-item, .case-item {
            background: rgba(179, 205, 224, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #B3CDE0;
        }

        .statute-item h5, .case-item h5 {
            color: #333;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .document-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-family: 'Noto Sans SC', serif;
        }

        .document-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 1rem;
        }

        .document-header h3 {
            font-size: 2rem;
            color: #333;
            font-weight: 700;
        }

        .download-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
            margin-top: 2rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 返回按钮样式 */
        .back-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #666;
            border: 2px solid #e0e0e0;
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            background: rgba(179, 205, 224, 0.1);
            border-color: #B3CDE0;
            color: #B3CDE0;
            transform: translateX(-3px);
        }

        /* 调试面板 */
        .debug-panel {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
        }

        .debug-panel button {
            margin-top: 5px;
            padding: 5px 10px;
            background: #333;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        /* 模拟法庭样式 */
        .court-simulation-container {
            max-width: 1000px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .court-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #dee2e6;
        }

        .court-title h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .court-subtitle {
            margin: 0.5rem 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .court-status {
            display: flex;
            align-items: center;
        }

        .status-indicator {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .court-participants {
            display: flex;
            justify-content: space-around;
            margin-bottom: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .participant {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .participant .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            transition: transform 0.3s ease;
        }

        .participant.judge .avatar {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        }

        .participant.plaintiff .avatar {
            background: linear-gradient(135deg, #00b894, #00cec9);
        }

        .participant.defendant .avatar {
            background: linear-gradient(135deg, #e17055, #fd79a8);
        }

        .participant span {
            font-weight: 500;
            color: #2c3e50;
        }

        .court-dialogue {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .dialogue-message {
            display: flex;
            margin-bottom: 1.5rem;
            opacity: 0;
            transform: translateY(20px);
            animation: messageSlideIn 0.5s ease forwards;
        }

        @keyframes messageSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dialogue-message.judge {
            justify-content: center;
        }

        .dialogue-message.plaintiff {
            justify-content: flex-start;
        }

        .dialogue-message.defendant {
            justify-content: flex-end;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin: 0 0.5rem;
            flex-shrink: 0;
        }

        .dialogue-message.judge .message-avatar {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        }

        .dialogue-message.plaintiff .message-avatar {
            background: linear-gradient(135deg, #00b894, #00cec9);
        }

        .dialogue-message.defendant .message-avatar {
            background: linear-gradient(135deg, #e17055, #fd79a8);
        }

        .message-bubble {
            max-width: 70%;
            padding: 1rem 1.5rem;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
        }

        .dialogue-message.judge .message-bubble {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #6c5ce7;
            text-align: center;
        }

        .dialogue-message.plaintiff .message-bubble {
            background: linear-gradient(135deg, #d1f2eb, #a3e4d7);
            border-left: 4px solid #00b894;
        }

        .dialogue-message.defendant .message-bubble {
            background: linear-gradient(135deg, #fdeaea, #f8d7da);
            border-right: 4px solid #e17055;
        }

        .message-speaker {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .dialogue-message.judge .message-speaker {
            color: #6c5ce7;
        }

        .dialogue-message.plaintiff .message-speaker {
            color: #00b894;
        }

        .dialogue-message.defendant .message-speaker {
            color: #e17055;
        }

        .message-content {
            line-height: 1.6;
            color: #2c3e50;
        }

        .court-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .court-btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .court-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .court-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .court-btn.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #dee2e6;
        }

        .court-btn.secondary:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .court-summary {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .court-summary h4 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .summary-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .summary-section h5 {
            color: #495057;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .summary-section ul {
            list-style: none;
            padding: 0;
        }

        .summary-section li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f3f4;
            color: #6c757d;
        }

        .summary-section li:last-child {
            border-bottom: none;
        }

        .probability-bar {
            position: relative;
            height: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            margin-top: 1rem;
        }

        .probability-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 15px;
            transition: width 2s ease;
            position: relative;
        }

        .probability-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 600;
            color: #2c3e50;
            z-index: 2;
        }

        /* 打字机效果 */
        .typing-cursor::after {
            content: '|';
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 右上角fayi标识 */
        .fayi-watermark {
            position: fixed;
            top: 20px;
            right: 20px;
            font-size: 0.8rem;
            color: #333; /* 修改为黑色 */
            font-weight: 300;
            z-index: 1000;
            pointer-events: none;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        /* 数据概览样式 */
        .data-overview-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(162, 217, 206, 0.1) 100%);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .overview-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 2rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .evidence-ratio .card-icon {
            background: linear-gradient(135deg, #B3CDE0, #A2D9CE);
        }

        .missing-evidence .card-icon {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        }

        .win-rate .card-icon {
            background: linear-gradient(135deg, #F5A623, #ff8c42);
        }

        .case-strength .card-icon {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .card-content {
            flex: 1;
        }

        .card-content h3 {
            font-size: 1rem;
            color: #666;
            margin: 0 0 0.5rem 0;
            font-weight: 500;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin: 0.5rem 0;
            line-height: 1;
        }

        .metric-desc {
            font-size: 0.9rem;
            color: #888;
            margin: 0;
        }

        /* 详细报告样式 */
        .detailed-report-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .tab-panels {
            margin-top: 2rem;
        }

        .tab-panel {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-panel.active {
            display: block;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .action-buttons .download-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 文档内容样式优化 */
        .document-body {
            line-height: 1.8;
            color: #333;
        }

        .party-info p {
            margin-bottom: 1rem;
            text-indent: 2em;
        }

        .case-cause p {
            margin-bottom: 1.5rem;
            text-align: center;
            font-weight: 500;
        }

        .claims-section, .facts-section, .evidence-section {
            margin-bottom: 2rem;
        }

        .claims-section h4, .facts-section h4, .evidence-section h4 {
            color: #B3CDE0;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .claims-section ol, .evidence-section ol {
            padding-left: 2rem;
        }

        .claims-section li, .evidence-section li {
            margin-bottom: 0.5rem;
        }

        .facts-section h5 {
            color: #666;
            font-size: 1rem;
            margin: 1.5rem 0 0.8rem 0;
            font-weight: 500;
        }

        .facts-section p {
            margin-bottom: 1rem;
            text-indent: 2em;
        }

        .closing-section {
            margin: 2rem 0;
            text-align: left;
        }

        .closing-section p {
            margin-bottom: 0.5rem;
        }

        .attachments-section {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .logo-text {
                font-size: 3rem;
            }

            .fayi-watermark {
                font-size: 0.7rem;
                top: 15px;
                right: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 右上角fayi水印 -->
    <div class="fayi-watermark">fayi</div>

    <!-- 欢迎首页 -->
    <div id="welcomePage" class="page active">
        <div class="welcome-container">
            <!-- 顶部Logo和标语 -->
            <header class="welcome-header">
                <div class="logo">
                    <h1 class="logo-text">法弈</h1>
                    <p class="tagline">洞见法律脉络，预见诉讼未来</p>
                </div>
            </header>

            <!-- 中央功能简介区 -->
            <main class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="4" fill="currentColor"/>
                            <circle cx="16" cy="16" r="3" fill="currentColor"/>
                            <circle cx="48" cy="16" r="3" fill="currentColor"/>
                            <circle cx="16" cy="48" r="3" fill="currentColor"/>
                            <circle cx="48" cy="48" r="3" fill="currentColor"/>
                            <path d="M32 28L19 19M32 28L45 19M32 36L19 45M32 36L45 45" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">案件智能解构</h3>
                    <p class="feature-desc">深入剖析案情，提炼关键要素</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M32 8L40 16H24L32 8Z" fill="currentColor"/>
                            <rect x="20" y="16" width="24" height="32" rx="2" fill="currentColor" opacity="0.7"/>
                            <circle cx="16" cy="32" r="4" fill="currentColor"/>
                            <circle cx="48" cy="32" r="4" fill="currentColor"/>
                            <path d="M20 32H16M44 32H48" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">证据链诊断</h3>
                    <p class="feature-desc">梳理现有证据，预警潜在缺口</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="16" y="12" width="32" height="40" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M24 20H40M24 28H40M24 36H36" stroke="white" stroke-width="2"/>
                            <path d="M32 8L36 12H28L32 8Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">法律与案例指引</h3>
                    <p class="feature-desc">智能匹配法规，精准推荐相似判例</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="20" y="8" width="24" height="48" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M28 16H36M28 24H36M28 32H36M28 40H32" stroke="white" stroke-width="2"/>
                            <path d="M16 20L20 16V24L16 20Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">诉讼文书生成</h3>
                    <p class="feature-desc">一键生成专业、规范的法律文书初稿</p>
                </div>
            </main>

            <!-- 底部体验按钮 -->
            <footer class="welcome-footer">
                <button id="startExperienceBtn" class="start-btn">
                    <span>立即体验</span>
                </button>
            </footer>
        </div>
    </div>

    <!-- 核心体验页 -->
    <div id="experiencePage" class="page">
        <div class="experience-container">
            <!-- 信息输入区 -->
            <section class="input-section">
                <h2 class="section-title">请告诉我们您遇到的问题</h2>
                <div class="input-grid">
                    <div class="input-group">
                        <label for="caseDescription">案情基本情况描述</label>
                        <textarea id="caseDescription" placeholder="请在这里详细描述事情的经过，涉及的人物、时间、地点等…"></textarea>
                    </div>
                    <div class="input-group">
                        <label for="legalClaim">您的主要诉讼请求</label>
                        <textarea id="legalClaim" placeholder="例如：要求对方赔偿损失10万元、要求对方履行合同等…"></textarea>
                    </div>
                </div>
                <div class="analyze-section">
                    <button id="analyzeBtn" class="analyze-btn">
                        <span>立即分析</span>
                    </button>
                </div>
            </section>

            <!-- 智能分析过程展示区 -->
            <section id="analysisSection" class="analysis-section" style="display: none;">
                <div class="analysis-background">
                    <canvas id="networkCanvas"></canvas>
                    <div class="analysis-content">
                        <div class="analysis-text">
                            <h3 id="analysisTitle">模型分析进度</h3>
                            <div id="analysisStage" class="analysis-stage">正在调用证据生成微调模型...</div>

                            <!-- 进度条 -->
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div id="progressFill" class="progress-fill"></div>
                                </div>
                                <div id="progressText" class="progress-text">0%</div>
                            </div>

                            <div class="progress-dots">
                                <span></span><span></span><span></span>
                            </div>
                        </div>
                        <button id="viewReportBtn" class="view-report-btn" style="display: none;">
                            查看证据链分析报告
                        </button>
                    </div>
                </div>
            </section>

            <!-- 分析成果展示区 -->
            <section id="resultsSection" class="results-section" style="display: none;">
                <!-- 证据链分析 -->
                <div id="evidenceChain" class="evidence-chain">
                    <h3>证据链分析图</h3>
                    <div class="evidence-timeline" id="evidenceTimeline">
                        <div class="evidence-container">
                            <!-- 左侧证据列表 -->
                            <div class="evidence-list" id="evidenceList">
                                <h4>证据详情</h4>
                                <!-- 证据详情列表将通过JavaScript动态生成 -->
                            </div>

                            <!-- 中间证据关系图 -->
                            <div class="evidence-graph" id="evidenceGraph">
                                <!-- 证据节点和连接线将通过JavaScript动态生成 -->
                            </div>

                            <!-- 右侧分析报告 -->
                            <div class="evidence-report" id="evidenceReport">
                                <h4>证据分析报告</h4>
                                <!-- 分析报告将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    <button id="viewLegalInsightsBtn" class="full-report-btn">
                        查看法律分析和案例指引
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 法律分析与案例指引页面 -->
    <div id="legalInsightsPage" class="page">
        <div class="experience-container">
            <section class="results-section">
                <div class="full-report">
                    <!-- 返回按钮 -->
                    <div style="margin-bottom: 2rem;">
                        <button id="backToEvidenceBtn" class="back-btn">
                            ← 返回证据链分析
                        </button>
                    </div>

                    <h2 class="section-title">法律分析与案例指引</h2>

                    <!-- 检索动画区域 -->
                    <div id="searchAnimation" class="analysis-background" style="margin-bottom: 3rem; height: 350px; padding: 3rem 2rem;">
                        <canvas id="searchCanvas"></canvas>
                        <div class="analysis-content" style="padding: 2rem 0;">
                            <div class="analysis-text" style="margin-top: 2rem;">
                                <h3 style="font-size: 1.2rem; line-height: 1.6; margin-bottom: 2rem; white-space: nowrap;">正在检索相关法律条文和案例...</h3>
                                <div class="progress-dots">
                                    <span></span><span></span><span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="legalInsightsContent" class="report-content" style="display: none;">
                        <!-- 法律分析内容将动态加载 -->
                    </div>
                    <button id="viewCourtSimulationBtn" class="full-report-btn" style="display: none;">
                        查看模拟法庭推演
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 模拟法庭推演页面 -->
    <div id="courtSimulationPage" class="page">
        <div class="experience-container">
            <section class="results-section">
                <div class="full-report">
                    <!-- 返回按钮 -->
                    <div style="margin-bottom: 2rem;">
                        <button id="backToLegalInsightsBtn" class="back-btn">
                            ← 返回法律分析与案例指引
                        </button>
                    </div>

                    <h2 class="section-title">模拟法庭推演</h2>
                    <div id="courtSimulationContent" class="report-content">
                        <!-- 模拟法庭内容将动态加载 -->
                    </div>
                    <button id="viewLegalDocumentBtn" class="full-report-btn" style="display: none;">
                        查看完整分析报告与起诉文书
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 完整分析报告和起诉文书页面 -->
    <div id="legalDocumentPage" class="page">
        <div class="experience-container">
            <section class="results-section">
                <div class="full-report">
                    <!-- 返回按钮 -->
                    <div style="margin-bottom: 2rem;">
                        <button id="backToCourtSimulationBtn" class="back-btn">
                            ← 返回模拟法庭推演
                        </button>
                    </div>

                    <h2 class="section-title">完整分析报告与起诉文书</h2>

                    <!-- 数据概览区域 -->
                    <div id="dataOverview" class="data-overview-section">
                        <div class="overview-cards">
                            <div class="overview-card evidence-ratio">
                                <div class="card-icon">📊</div>
                                <div class="card-content">
                                    <h3>证据完整度</h3>
                                    <div class="metric-value">80%</div>
                                    <div class="metric-desc">5项证据中已有4项</div>
                                </div>
                            </div>

                            <div class="overview-card missing-evidence">
                                <div class="card-icon">⚠️</div>
                                <div class="card-content">
                                    <h3>关键缺失</h3>
                                    <div class="metric-value">1项</div>
                                    <div class="metric-desc">第三方质检报告</div>
                                </div>
                            </div>

                            <div class="overview-card win-rate">
                                <div class="card-icon">⚖️</div>
                                <div class="card-content">
                                    <h3>预估胜率</h3>
                                    <div class="metric-value">78%</div>
                                    <div class="metric-desc">论证强度与证据完整度平均</div>
                                </div>
                            </div>

                            <div class="overview-card case-strength">
                                <div class="card-icon">💪</div>
                                <div class="card-content">
                                    <h3>论证强度</h3>
                                    <div class="metric-value">75%</div>
                                    <div class="metric-desc">基于法理和证据</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细报告内容 -->
                    <div id="detailedReport" class="detailed-report-section">
                        <div class="report-tabs">
                            <button class="tab-btn active" data-tab="evidence-analysis">证据链分析</button>
                            <button class="tab-btn" data-tab="legal-insights">法律分析</button>
                            <button class="tab-btn" data-tab="court-simulation">模拟推演</button>
                            <button class="tab-btn" data-tab="legal-document">起诉文书</button>
                        </div>

                        <div class="tab-panels">
                            <!-- 证据链分析面板 -->
                            <div id="evidence-analysis" class="tab-panel active">
                                <div id="evidenceAnalysisContent" class="report-content">
                                    <!-- 证据分析内容将动态加载 -->
                                </div>
                            </div>

                            <!-- 法律分析面板 -->
                            <div id="legal-insights" class="tab-panel">
                                <div id="legalInsightsDetailContent" class="report-content">
                                    <!-- 法律分析内容将动态加载 -->
                                </div>
                            </div>

                            <!-- 模拟推演面板 -->
                            <div id="court-simulation" class="tab-panel">
                                <div id="courtSimulationDetailContent" class="report-content">
                                    <!-- 模拟推演内容将动态加载 -->
                                </div>
                            </div>

                            <!-- 起诉文书面板 -->
                            <div id="legal-document" class="tab-panel">
                                <div id="legalDocumentContent" class="report-content">
                                    <!-- 起诉文书内容将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button id="downloadReportBtn" class="download-btn">
                            📄 下载完整报告
                        </button>
                        <button id="downloadDocumentBtn" class="download-btn">
                            📋 下载起诉文书
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- 调试面板 - 已隐藏 -->
    <div id="debugPanel" class="debug-panel" style="display: none;">
        <div id="debugInfo">调试信息：<br></div>
        <button onclick="toggleDebug()">关闭</button>
    </div>

    <script>
        // 调试功能（已简化）
        function log(message) {
            // 只在控制台输出，不显示在页面上
            console.log(message);
        }

        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 页面切换函数
        function switchToExperiencePage() {
            log('开始切换到体验页面');
            const welcomePage = document.getElementById('welcomePage');
            const experiencePage = document.getElementById('experiencePage');

            if (!welcomePage || !experiencePage) {
                log('错误：找不到页面元素');
                return;
            }

            welcomePage.classList.remove('active');
            experiencePage.classList.add('active');
            log('页面切换完成');

            // 1秒后自动填入示例数据
            setTimeout(() => {
                fillExampleData();
            }, 1000);
        }

        // 填充示例数据
        function fillExampleData() {
            log('开始填充示例数据');
            const caseDescription = document.getElementById('caseDescription');
            const legalClaim = document.getElementById('legalClaim');

            if (!caseDescription || !legalClaim) {
                log('错误：找不到输入框');
                return;
            }

            const sampleCase = "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫'幻影X Pro'的笔记本，订单号是一串数字880123456789。18号电脑就到了，本来还挺开心的。结果好家伙，用了还不到一个月，从8月10号开始吧，这电脑就老是无缘无故蓝屏，然后卡死不动，机身烫得跟个暖手宝似的，我的好多工作文件都因为这个搞丢了！";
            const sampleClaim = "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！";

            // 先输入案情描述，完成后再输入诉讼请求
            typeWriter(caseDescription, sampleCase, 30, () => {
                // 案情描述输入完成后，等待500ms再开始输入诉讼请求
                setTimeout(() => {
                    typeWriter(legalClaim, sampleClaim, 30);
                }, 500);
            });
        }

        // 打字机效果
        function typeWriter(element, text, speed, callback) {
            element.value = '';
            let i = 0;

            function type() {
                if (i < text.length) {
                    element.value += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else if (callback) {
                    // 输入完成后执行回调函数
                    callback();
                }
            }

            type();
        }

        // 分析功能
        function startAnalysis() {
            log('开始分析');
            const caseDescription = document.getElementById('caseDescription').value;
            const legalClaim = document.getElementById('legalClaim').value;

            if (!caseDescription.trim() || !legalClaim.trim()) {
                alert('请填写案情描述和诉讼请求');
                return;
            }

            log('输入验证通过，开始显示分析过程');

            // 显示分析区域
            const analysisSection = document.getElementById('analysisSection');
            analysisSection.style.display = 'block';

            // 滚动到分析区域
            setTimeout(() => {
                analysisSection.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 开始网络动画
            startNetworkAnimation();

            // 开始进度条动画
            startProgressAnimation();
        }

        // 进度条动画和阶段更新
        function startProgressAnimation() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const analysisStage = document.getElementById('analysisStage');
            const viewReportBtn = document.getElementById('viewReportBtn');

            const stages = [
                { progress: 0, text: '正在调用证据生成微调模型...', duration: 600 },
                { progress: 15, text: '正在解析案情描述...', duration: 900 },
                { progress: 35, text: '正在识别关键法律要素...', duration: 1000 },
                { progress: 55, text: '正在构建证据关系图...', duration: 1100 },
                { progress: 75, text: '正在分析证据完整性...', duration: 900 },
                { progress: 92, text: '正在生成分析报告...', duration: 700 },
                { progress: 100, text: '分析完成！', duration: 600 }
            ];

            let currentStage = 0;

            function updateStage() {
                if (currentStage < stages.length) {
                    const stage = stages[currentStage];

                    // 更新进度条
                    progressFill.style.width = stage.progress + '%';
                    progressText.textContent = stage.progress + '%';

                    // 更新阶段文本
                    analysisStage.textContent = stage.text;

                    // 如果是最后一个阶段，添加完成样式
                    if (stage.progress === 100) {
                        analysisStage.classList.add('completed');
                    }

                    log(`分析阶段 ${currentStage + 1}: ${stage.text} (${stage.progress}%)`);

                    currentStage++;

                    // 如果是最后一个阶段，显示查看报告按钮
                    if (currentStage === stages.length) {
                        setTimeout(() => {
                            viewReportBtn.style.display = 'block';
                            viewReportBtn.style.animation = 'fadeIn 0.5s ease';
                            log('查看报告按钮已显示');
                        }, stage.duration);
                    } else {
                        // 继续下一个阶段
                        setTimeout(updateStage, stage.duration);
                    }
                }
            }

            // 开始第一个阶段
            updateStage();
        }

        // 网络动画
        function startNetworkAnimation() {
            const canvas = document.getElementById('networkCanvas');
            if (!canvas) {
                log('找不到画布元素');
                return;
            }

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 节点和连接
            const nodes = [];
            const connections = [];
            const keywords = ['借条', '转账记录', '违约', '张三', '合同', '证据', '质检报告', '客服记录'];

            // 创建节点
            for (let i = 0; i < 8; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    radius: Math.random() * 5 + 3,
                    keyword: keywords[i],
                    alpha: Math.random()
                });
            }

            // 创建连接
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    if (Math.random() < 0.3) {
                        connections.push({ from: i, to: j, alpha: 0 });
                    }
                }
            }

            let animationId;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 更新和绘制连接
                connections.forEach(conn => {
                    const fromNode = nodes[conn.from];
                    const toNode = nodes[conn.to];

                    conn.alpha = Math.min(conn.alpha + 0.01, 0.3);

                    ctx.strokeStyle = `rgba(179, 205, 224, ${conn.alpha})`;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(fromNode.x, fromNode.y);
                    ctx.lineTo(toNode.x, toNode.y);
                    ctx.stroke();
                });

                // 更新和绘制节点
                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.003 + node.x * 0.01) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = `rgba(179, 205, 224, ${node.alpha})`;
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '12px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 5);
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();
            log('网络动画已启动');
        }

        // 显示证据链
        function showEvidenceChain() {
            log('显示证据链分析');

            // 显示结果区域
            const resultsSection = document.getElementById('resultsSection');
            resultsSection.style.display = 'block';

            // 滚动到结果区域
            setTimeout(() => {
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 生成证据链节点
            generateEvidenceNodes();
        }

        // 生成证据链图
        function generateEvidenceNodes() {
            const graph = document.getElementById('evidenceGraph');
            const list = document.getElementById('evidenceList');
            const report = document.getElementById('evidenceReport');

            graph.innerHTML = '';
            list.innerHTML = '<h4>证据详情</h4>';
            report.innerHTML = '<h4>证据分析报告</h4>';

            // 证据数据，包含位置信息（考虑容器内边距20px，节点宽度140px）
            // 有效绘图区域宽度约为容器宽度-40px（左右各20px内边距）
            const evidenceData = [
                { id: 'order', label: "网络购物订单", type: "evidence_owned", desc: "订单号880123456789，购买时间2025年7月15日", x: 10, y: 60 },
                { id: 'payment', label: "在线支付凭证", type: "evidence_owned", desc: "支付金额12,888元的电子凭证", x: 300, y: 60 },
                { id: 'contract', label: "建立买卖关系", type: "conclusion", desc: "通过订单和支付记录证明双方存在买卖合同关系", x: 155, y: 180 },
                { id: 'malfunction', label: "故障现象录像", type: "evidence_owned", desc: "记录设备故障的视频和照片证据", x: 480, y: 100 },
                { id: 'service', label: "客服沟通记录", type: "evidence_owned", desc: "完整的客服对话记录，证明商家推诿责任", x: 650, y: 100 },
                { id: 'defect', label: "产品质量缺陷", type: "conclusion", desc: "设备频繁蓝屏、死机、过热等故障现象", x: 565, y: 240 },
                { id: 'inspection', label: "第三方质检报告", type: "evidence_gap", desc: "⚠️ 缺失：权威机构出具的产品质量检测报告", x: 150, y: 360 },
                { id: 'claim', label: "退款退赔诉求", type: "final_claim", desc: "基于以上证据支持退款12,888元及误工费2,000元的诉求", x: 350, y: 480 }
            ];

            // 连接关系
            const connections = [
                { from: 'order', to: 'contract' },
                { from: 'payment', to: 'contract' },
                { from: 'malfunction', to: 'defect' },
                { from: 'service', to: 'defect' },
                { from: 'contract', to: 'claim' },
                { from: 'defect', to: 'claim' },
                { from: 'inspection', to: 'claim', type: 'gap' }
            ];

            // 创建SVG容器用于连接线和箭头
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.width = '100%';
            svg.style.height = '100%';
            svg.style.pointerEvents = 'none';
            svg.style.zIndex = '1';

            // 定义箭头标记
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

            const arrowMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarker.setAttribute('id', 'arrowhead');
            arrowMarker.setAttribute('markerWidth', '8');
            arrowMarker.setAttribute('markerHeight', '6');
            arrowMarker.setAttribute('refX', '7');
            arrowMarker.setAttribute('refY', '3');
            arrowMarker.setAttribute('orient', 'auto');

            const arrowPath = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPath.setAttribute('points', '0 0, 8 3, 0 6');
            arrowPath.setAttribute('fill', '#B3CDE0');
            arrowMarker.appendChild(arrowPath);
            defs.appendChild(arrowMarker);

            const arrowMarkerGap = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarkerGap.setAttribute('id', 'arrowhead-gap');
            arrowMarkerGap.setAttribute('markerWidth', '8');
            arrowMarkerGap.setAttribute('markerHeight', '6');
            arrowMarkerGap.setAttribute('refX', '7');
            arrowMarkerGap.setAttribute('refY', '3');
            arrowMarkerGap.setAttribute('orient', 'auto');

            const arrowPathGap = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPathGap.setAttribute('points', '0 0, 8 3, 0 6');
            arrowPathGap.setAttribute('fill', '#ff6b6b');
            arrowMarkerGap.appendChild(arrowPathGap);
            defs.appendChild(arrowMarkerGap);

            svg.appendChild(defs);
            graph.appendChild(svg);

            // 计算连接线的起点和终点（指向节点边框而不是中心）
            function calculateConnectionPoints(fromNode, toNode) {
                const fromCenterX = fromNode.x + 70; // 节点宽度140px的一半
                const fromCenterY = fromNode.y + 45; // 节点高度90px的一半
                const toCenterX = toNode.x + 70;
                const toCenterY = toNode.y + 45;

                const dx = toCenterX - fromCenterX;
                const dy = toCenterY - fromCenterY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // 根据节点类型计算连接半径
                function getNodeRadius(node) {
                    if (node.type === 'evidence_gap') {
                        // 圆形节点，使用圆形半径
                        return Math.min(70, 45); // 取宽高的较小值作为半径
                    } else if (node.type === 'conclusion') {
                        // 菱形节点，使用对角线的一半
                        return Math.sqrt(70*70 + 45*45) / 2;
                    } else {
                        // 矩形节点，根据角度计算
                        const angle = Math.atan2(dy, dx);
                        const absAngle = Math.abs(angle);
                        if (absAngle < Math.atan2(90, 140)) {
                            return 70; // 连接到左右边
                        } else {
                            return 45; // 连接到上下边
                        }
                    }
                }

                const fromRadius = getNodeRadius(fromNode);
                const toRadius = getNodeRadius(toNode);

                const fromX = fromCenterX + (dx / distance) * fromRadius;
                const fromY = fromCenterY + (dy / distance) * fromRadius;
                const toX = toCenterX - (dx / distance) * toRadius;
                const toY = toCenterY - (dy / distance) * toRadius;

                return { fromX, fromY, toX, toY };
            }

            // 创建连接线
            connections.forEach((conn, index) => {
                const fromNode = evidenceData.find(n => n.id === conn.from);
                const toNode = evidenceData.find(n => n.id === conn.to);

                if (fromNode && toNode) {
                    const points = calculateConnectionPoints(fromNode, toNode);

                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', points.fromX);
                    line.setAttribute('y1', points.fromY);
                    line.setAttribute('x2', points.toX);
                    line.setAttribute('y2', points.toY);
                    line.style.stroke = conn.type === 'gap' ? '#ff6b6b' : '#B3CDE0';
                    line.style.strokeWidth = '3';
                    line.style.opacity = '0';
                    line.style.animation = `lineAppear 1s ease ${index * 0.3}s forwards`;
                    line.setAttribute('marker-end', conn.type === 'gap' ? 'url(#arrowhead-gap)' : 'url(#arrowhead)');

                    if (conn.type === 'gap') {
                        line.style.strokeDasharray = '8,4';
                    }

                    svg.appendChild(line);
                }
            });

            // 创建证据节点
            evidenceData.forEach((node, index) => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `evidence-node ${node.type}`;
                nodeElement.style.left = `${node.x}px`;
                nodeElement.style.top = `${node.y}px`;
                nodeElement.style.animationDelay = `${index * 0.2}s`;
                nodeElement.style.zIndex = '2';

                nodeElement.innerHTML = `<h4>${node.label}</h4>`;
                graph.appendChild(nodeElement);

                // 创建左侧列表项
                const listItem = document.createElement('div');
                listItem.className = `evidence-list-item ${node.type}`;
                listItem.innerHTML = `
                    <h5>${node.label}</h5>
                    <p>${node.desc}</p>
                `;
                list.appendChild(listItem);
            });

            // 生成右侧分析报告
            generateEvidenceReport(report);

            log('证据链图生成完成');
        }

        // 生成证据分析报告
        function generateEvidenceReport(reportContainer) {
            const reportContent = `
                <div class="report-section">
                    <h5>证据完整性评估</h5>
                    <p>当前证据链完整度：<strong>75%</strong></p>
                    <div class="report-highlight">
                        <p><strong>优势证据：</strong>购买凭证完整，故障现象有记录，客服推诿有证据。</p>
                    </div>
                    <div class="report-highlight">
                        <p><strong>关键缺口：</strong>缺少第三方权威质检报告，可能影响胜诉概率。</p>
                    </div>
                </div>

                <div class="report-section">
                    <h5>证据链逻辑分析</h5>
                    <p>1. <strong>合同关系确立</strong>：订单+支付凭证 → 买卖关系成立</p>
                    <p>2. <strong>违约事实认定</strong>：故障录像+客服记录 → 产品质量缺陷</p>
                    <p>3. <strong>责任归属</strong>：质量缺陷+合同关系 → 商家违约责任</p>
                </div>

                <div class="report-section">
                    <h5>补强建议</h5>
                    <p>• 立即委托有资质的检测机构进行产品质量检测</p>
                    <p>• 收集更多同型号产品的质量投诉案例</p>
                    <p>• 整理因设备故障导致的具体损失证据</p>
                </div>

                <div class="report-section">
                    <h5>胜诉概率预测</h5>
                    <p>基于当前证据：<strong style="color: #F5A623;">70-80%</strong></p>
                    <p>补强证据后：<strong style="color: #A2D9CE;">85-95%</strong></p>
                </div>
            `;

            reportContainer.innerHTML += reportContent;
        }

        // 显示法律分析页面
        function showLegalInsights() {
            log('显示法律分析页面');
            switchToPage('legalInsightsPage');
            startLegalAnalysisAnimation();
        }

        // 显示模拟法庭页面
        function showCourtSimulation() {
            log('显示模拟法庭页面');
            switchToPage('courtSimulationPage');
            loadCourtSimulationContent();
        }

        // 显示完整分析报告和起诉文书页面
        function showLegalDocument() {
            log('显示完整分析报告页面');
            switchToPage('legalDocumentPage');
            loadCompleteReport();
        }

        // 加载完整报告
        function loadCompleteReport() {
            log('开始加载完整报告');

            // 加载各个模块的内容
            loadEvidenceAnalysisDetail();
            loadLegalInsightsDetail();
            loadCourtSimulationDetail();
            loadLegalDocumentDetail();

            // 初始化标签页切换
            setTimeout(() => {
                initReportTabs();
            }, 100);
        }

        // 初始化报告标签页
        function initReportTabs() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const targetTab = btn.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabPanels.forEach(p => p.classList.remove('active'));

                    // 激活当前标签
                    btn.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }

        // 加载证据分析详情
        function loadEvidenceAnalysisDetail() {
            const container = document.getElementById('evidenceAnalysisContent');
            if (!container) return;

            container.innerHTML = `
                <h4>证据链完整性分析</h4>
                <div class="report-highlight">
                    <strong>证据完整度：80%</strong> - 当前已收集到关键证据4项，仍缺失1项核心证据
                </div>

                <h5>已有证据清单</h5>
                <ul>
                    <li><strong>网络购物订单：</strong>证明买卖合同关系成立，订单号880123456789</li>
                    <li><strong>在线支付凭证：</strong>证明已支付货款12,888元</li>
                    <li><strong>故障现象录像：</strong>记录电脑蓝屏、死机等质量问题</li>
                    <li><strong>客服沟通记录：</strong>证明被告拒绝履行三包义务</li>
                </ul>

                <h5>关键证据缺口</h5>
                <div class="report-highlight" style="border-left-color: #ff6b6b; background: rgba(255, 107, 107, 0.1);">
                    <strong>第三方质检报告：</strong>这是锁定被告责任的核心证据。当前证据主要依赖原告单方陈述，被告可能抗辩称故障系人为原因导致。建议立即将设备送至具有司法鉴定资质的第三方检测机构进行检测。
                </div>

                <h5>证据补强建议</h5>
                <ol>
                    <li>尽快获取第三方权威检测报告</li>
                    <li>公证保全与客服的完整沟通记录</li>
                    <li>收集同型号产品的质量投诉信息</li>
                    <li>准备误工损失的相关证明材料</li>
                </ol>
            `;
        }

        // 加载法律分析详情
        function loadLegalInsightsDetail() {
            const container = document.getElementById('legalInsightsDetailContent');
            if (!container) return;

            container.innerHTML = `
                <h4>适用法律条文</h4>
                <div class="statute-item">
                    <h5>《中华人民共和国消费者权益保护法》第二十四条</h5>
                    <p>经营者提供的商品或者服务不符合质量要求的，消费者可以依照国家规定、当事人约定退货，或者要求经营者履行更换、修理等义务。没有国家规定和当事人约定的，消费者可以自收到商品之日起七日内退货；七日后符合法定解除合同条件的，消费者可以及时退货，不符合法定解除合同条件的，可以要求经营者履行更换、修理等义务。</p>
                </div>

                <div class="statute-item">
                    <h5>《中华人民共和国民法典》第五百七十七条</h5>
                    <p>当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。</p>
                </div>

                <h4>相关指导案例</h4>
                <div class="case-item">
                    <h5>刘某诉深圳市星辰科技有限公司买卖合同纠纷案</h5>
                    <p><strong>案号：</strong>（2024）粤0305民初8756号</p>
                    <p><strong>案情概要：</strong>原告刘某于2024年3月通过被告官方网站购买"星辰游戏本Pro"笔记本电脑一台，价款13,999元。使用20天后设备频繁出现蓝屏死机、CPU过热降频、显卡驱动崩溃等故障。原告多次联系客服要求退货，被告以"软件兼容性问题"为由拒绝，仅同意维修。</p>
                    <p><strong>争议焦点：</strong>①产品故障是否属于质量缺陷；②消费者是否有权要求退货；③经营者的举证责任范围。</p>
                    <p><strong>法院认定：</strong>①被告作为专业电脑销售商，应保证产品在正常使用条件下的稳定性；②原告提供的故障录屏、系统日志、客服聊天记录等证据形成完整证据链，足以证明产品存在质量缺陷；③被告未能提供充分证据证明故障系原告使用不当所致，应承担举证不能的不利后果。</p>
                    <p><strong>判决结果：</strong>支持原告退货退款请求，被告退还货款13,999元并承担诉讼费。</p>
                    <p><strong>借鉴价值：</strong>明确了网购电子产品质量纠纷中的举证责任分配，消费者只需证明产品存在故障即可，经营者需证明故障非产品质量问题。</p>
                </div>

                <div class="case-item">
                    <h5>陈某诉北京智能设备有限公司产品责任纠纷案</h5>
                    <p><strong>案号：</strong>（2023）京0108民初15432号</p>
                    <p><strong>案情概要：</strong>原告陈某在被告天猫旗舰店购买"智能办公本X1"笔记本电脑，价款11,888元。收货后第15天开始出现间歇性黑屏、键盘失灵、散热异常等问题。被告客服要求原告自费邮寄检测，原告拒绝并要求退货。</p>
                    <p><strong>关键证据：</strong>①购买订单及支付凭证；②故障现象视频录像12段；③与客服沟通的完整聊天记录；④第三方维修店出具的初步检测意见。</p>
                    <p><strong>法院观点：</strong>网络购物中，经营者对产品质量承担更严格的保证义务。消费者在"三包"期内发现质量问题，有权选择退货、换货或修理。经营者不得以"需要检测"、"可能人为损坏"等理由拒绝或拖延履行义务。</p>
                    <p><strong>判决结果：</strong>被告退还货款11,888元，赔偿原告交通费、误工费等合理损失1,200元。</p>
                    <p><strong>指导意义：</strong>确立了消费者在产品质量争议中的优势地位，经营者应主动承担质量保证责任。</p>
                </div>

                <h4>法律风险评估</h4>
                <div class="report-section">
                    <h5>有利因素</h5>
                    <ul>
                        <li>产品在三包期内出现故障，符合法定退货条件</li>
                        <li>有完整的购买和沟通记录作为证据支撑</li>
                        <li>被告明确拒绝退货，构成违约</li>
                    </ul>
                </div>

                <div class="report-section">
                    <h5>不利因素</h5>
                    <ul>
                        <li>缺乏第三方权威检测报告</li>
                        <li>被告可能抗辩故障系人为原因导致</li>
                        <li>误工损失的证明可能不够充分</li>
                    </ul>
                </div>
            `;
        }

        // 加载模拟推演详情
        function loadCourtSimulationDetail() {
            const container = document.getElementById('courtSimulationDetailContent');
            if (!container) return;

            container.innerHTML = `
                <h4>争议焦点分析</h4>
                <div class="report-section">
                    <h5>🎯 主要争议点</h5>
                    <ol>
                        <li><strong>产品质量问题的认定：</strong>电脑故障是否属于产品本身质量缺陷</li>
                        <li><strong>退货条件的满足：</strong>是否符合法定或约定的退货条件</li>
                        <li><strong>损失赔偿的合理性：</strong>误工损失的数额及因果关系</li>
                    </ol>
                </div>

                <h4>双方可能的主张和抗辩</h4>
                <div class="report-section">
                    <h5>💪 我方优势论点</h5>
                    <ul>
                        <li>产品在三包期内出现严重性能故障，符合法定退货条件</li>
                        <li>被告以不实理由（如软件不兼容）推卸责任，属于恶意违约</li>
                        <li>产品质量问题直接导致了原告的误工损失，应予赔偿</li>
                        <li>有完整的购买凭证和沟通记录作为证据支撑</li>
                    </ul>
                </div>

                <div class="report-section">
                    <h5>⚠️ 对方可能抗辩</h5>
                    <ul>
                        <li>故障可能是由原告自行安装的第三方软件或病毒导致，非产品本身质量问题</li>
                        <li>原告未能提供权威的检测报告证明产品存在固有缺陷</li>
                        <li>原告主张的误工损失缺乏充分的证据支持</li>
                        <li>产品仍在保修期内，应优先选择维修而非退货</li>
                    </ul>
                </div>

                <h4>应对策略建议</h4>
                <div class="report-section">
                    <h5>🎯 核心策略</h5>
                    <ol>
                        <li><strong>立即获取第三方检测报告：</strong>将设备送至具有司法鉴定资质的第三方检测机构进行检测，获取产品存在质量缺陷的直接证据</li>
                        <li><strong>固化证据材料：</strong>整理并公证与客服的完整沟通记录，固定被告消极应对和推诿责任的证据</li>
                        <li><strong>完善损失证明：</strong>准备误工损失的相关证据，如劳动合同、请假记录或因设备问题导致项目延误的沟通邮件等</li>
                    </ol>
                </div>

                <h4>胜诉概率评估</h4>
                <div class="probability-bar">
                    <div class="probability-fill" style="width: 78%;">
                        <div class="probability-text">78%</div>
                    </div>
                </div>
                <p style="margin-top: 1rem; color: #666;">基于现有证据和类似案例分析，预估胜诉概率为78%。如能补强第三方检测报告，胜诉概率可提升至85%以上。</p>
            `;
        }

        // 加载起诉文书详情
        function loadLegalDocumentDetail() {
            const container = document.getElementById('legalDocumentContent');
            if (!container) return;

            container.innerHTML = `
                <div class="document-content">
                    <div class="document-header">
                        <h3>民事起诉状</h3>
                    </div>

                    <div class="document-body">
                        <div class="party-info">
                            <p><strong>原告：</strong>张三，男，1995年10月20日出生，汉族，身份证号：11010119951020XXXX，住址：北京市朝阳区建国路XX号院X号楼X单元XXX室，联系电话：138-0013-8000。</p>

                            <p><strong>被告：</strong>未来科技电脑有限公司，统一社会信用代码：91440300MA5GXXXXXX，住所地：广东省深圳市南山区科技园路XX号，法定代表人：李四，联系电话：0755-88886666。</p>
                        </div>

                        <div class="case-cause">
                            <p><strong>案由：</strong>买卖合同纠纷</p>
                        </div>

                        <div class="claims-section">
                            <h4>诉讼请求</h4>
                            <ol>
                                <li>判令被告立即退还原告购物款人民币12,888元；</li>
                                <li>判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；</li>
                                <li>判令被告承担本案全部诉讼费用。</li>
                            </ol>
                        </div>

                        <div class="facts-section">
                            <h4>事实与理由</h4>

                            <h5>一、消费事实基本情况</h5>
                            <p>原告于2025年7月15日，通过被告运营的官方网站购买了"幻影X Pro"型笔记本电脑一台，并通过在线支付方式支付货款共计人民币12,888元，相关订单号为880123456789。该商品由被告安排物流并于同年7月18日送达原告处，双方之间的买卖合同关系依法成立且生效。</p>

                            <h5>二、涉案产品存在严重质量问题且被告拒绝履行法定义务</h5>
                            <p><strong>1. 产品存在严重质量缺陷，无法实现合同目的：</strong></p>
                            <p>原告在正常使用该电脑不足一个月后（自2025年8月10日起），设备便频繁出现无故蓝屏死机、系统崩溃以及机身异常过热等严重性能故障。上述故障直接导致原告无法正常使用该产品进行工作和学习，甚至造成重要数据丢失，其质量状况远未达到国家规定的合格标准及消费者正常使用的要求。</p>

                            <p><strong>2. 被告消极应对，拒绝履行三包义务及法定的退货责任：</strong></p>
                            <p>原告发现问题后，第一时间联系被告客服并按要求提交了故障证明材料。然而，被告不仅未能正视产品自身的质量问题，反而以"可能系软件不兼容"等无端理由进行推诿，并明确拒绝原告基于产品质量问题提出的退货退款的合法要求，其行为严重违反了《中华人民共和国消费者权益保护法》等相关法律规定，构成根本违约。</p>

                            <h5>三、本案的法律依据</h5>
                            <p><strong>1. 原告的退货退款请求权具有充分法律依据：</strong></p>
                            <p>根据《中华人民共和国消费者权益保护法》第二十四条之规定，经营者提供的商品不符合质量要求的，消费者有权要求退货。本案中，被告提供的电脑存在严重性能故障，原告依法享有主张全额退款的权利。</p>

                            <p><strong>2. 原告的损失赔偿请求权合理合法：</strong></p>
                            <p>根据《中华人民共和国民法典》第五百七十七条及第五百八十四条，因被告的违约行为（提供不合格产品），导致原告为解决纠纷耗费了大量时间精力，造成了实际的误工损失。因此，被告理应赔偿原告因此遭受的直接经济损失。</p>
                        </div>

                        <div class="evidence-section">
                            <h4>证据清单及来源</h4>
                            <ol>
                                <li>购买凭证：包括但不限于网络购物订单截图、在线支付记录凭证，用以证明原被告之间存在买卖合同关系及具体的交易金额。</li>
                                <li>质量缺陷证据：包括电脑蓝屏、死机的故障录像或照片；与被告客服的完整在线聊天记录截图或通话录音，用以证明产品存在质量问题且被告拒绝履行法定义务。</li>
                                <li>损失证明材料：原告工作单位出具的误工证明、劳动合同及工资流水，用以证明因处理本纠纷所造成的实际误工损失数额。</li>
                            </ol>
                        </div>

                        <div class="closing-section">
                            <p>此致</p>
                            <p><strong>XX市XX区人民法院</strong></p>
                            <br>
                            <p style="text-align: right;">起诉人：张三（手写签名）</p>
                            <p style="text-align: right;">二〇二五年八月二十四日</p>
                        </div>

                        <div class="attachments-section">
                            <h4>附项</h4>
                            <ol>
                                <li>本起诉状副本1份；</li>
                                <li>证据材料复印件1套；</li>
                                <li>原告身份证复印件1份。</li>
                            </ol>
                        </div>
                    </div>
                </div>
            `;
        }

        // 页面切换通用函数
        function switchToPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                // 滚动到页面顶部
                setTimeout(() => {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }, 100);
            }
        }

        // 返回证据链分析页面
        function backToEvidenceChain() {
            log('返回证据链分析页面');
            switchToPage('experiencePage');

            // 确保证据链分析结果是显示的
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.style.display = 'block';
                // 滚动到证据链分析区域
                setTimeout(() => {
                    resultsSection.scrollIntoView({ behavior: 'smooth' });
                }, 200);
            }
        }

        // 返回法律分析与案例指引页面
        function backToLegalInsights() {
            log('返回法律分析与案例指引页面');
            switchToPage('legalInsightsPage');
        }

        // 返回模拟法庭推演页面
        function backToCourtSimulation() {
            log('返回模拟法庭推演页面');
            switchToPage('courtSimulationPage');
        }

        // 开始法律分析动画
        function startLegalAnalysisAnimation() {
            log('开始法律分析动画');

            // 显示检索动画
            const searchAnimation = document.getElementById('searchAnimation');
            const legalContent = document.getElementById('legalInsightsContent');
            const nextButton = document.getElementById('viewCourtSimulationBtn');

            if (searchAnimation) {
                searchAnimation.style.display = 'block';
                startSearchAnimation();
            }

            // 3秒后显示第一部分内容（法律条文）
            setTimeout(() => {
                loadLegalAnalysisFirstPart();
                legalContent.style.display = 'block';
                searchAnimation.style.display = 'none';

                // 立即显示第二部分内容（案例检索动画）
                loadLegalAnalysisSecondPart();
            }, 3000);
        }

        // 检索动画
        function startSearchAnimation() {
            const canvas = document.getElementById('searchCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 创建法律条文关键词节点
            const legalKeywords = ['消费者权益保护法', '民法典', '产品质量法', '违约责任', '三包规定', '举证责任'];
            const nodes = [];

            for (let i = 0; i < legalKeywords.length; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 1.5,
                    vy: (Math.random() - 0.5) * 1.5,
                    radius: Math.random() * 4 + 4,
                    keyword: legalKeywords[i],
                    alpha: Math.random() * 0.5 + 0.5,
                    color: `hsl(${200 + Math.random() * 60}, 70%, 60%)`
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.002 + node.x * 0.01) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = node.color.replace(')', `, ${node.alpha})`).replace('hsl', 'hsla');
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '11px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 8);
                });

                // 绘制连接线
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        const dx = nodes[j].x - nodes[i].x;
                        const dy = nodes[j].y - nodes[i].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 150) {
                            const opacity = (150 - distance) / 150 * 0.3;
                            ctx.strokeStyle = `rgba(179, 205, 224, ${opacity})`;
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(nodes[i].x, nodes[i].y);
                            ctx.lineTo(nodes[j].x, nodes[j].y);
                            ctx.stroke();
                        }
                    }
                }

                requestAnimationFrame(animate);
            }

            animate();
        }

        // 加载法律分析第一部分（法律条文）
        function loadLegalAnalysisFirstPart() {
            const container = document.getElementById('legalInsightsContent');
            if (!container) return;

            container.innerHTML = `
                <div style="animation: fadeIn 0.8s ease;">
                    <h4>相关法律条文</h4>
                    <div class="statute-item">
                        <h5>《中华人民共和国消费者权益保护法》第二十四条</h5>
                        <p>经营者提供的商品或者服务不符合质量要求的，消费者可以依照国家规定、当事人约定退货，或者要求经营者履行更换、修理等义务。没有国家规定和当事人约定的，消费者可以自收到商品之日起七日内退货；七日后符合法定解除合同条件的，消费者可以及时退货，不符合法定解除合同条件的，可以要求经营者履行更换、修理等义务。</p>
                    </div>
                    <div class="statute-item">
                        <h5>《中华人民共和国民法典》第五百七十七条</h5>
                        <p>当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。</p>
                    </div>
                    <div class="statute-item">
                        <h5>《中华人民共和国产品质量法》第四十条</h5>
                        <p>售出的产品有下列情形之一的，销售者应当负责修理、更换、退货；给购买产品的消费者造成损失的，销售者应当赔偿损失：（一）不具备产品应当具备的使用性能而事先未作说明的；（二）不符合在产品或者其包装上注明采用的产品标准的；（三）不符合以产品说明、实物样品等方式表明的质量状况的。</p>
                    </div>
                </div>
                <div id="secondPartContent" style="display: none;">
                    <!-- 案例检索动画区域 -->
                    <div id="caseSearchAnimation" class="analysis-background" style="margin-top: 1rem; margin-bottom: 2rem; height: 300px; padding: 2rem;">
                        <canvas id="caseSearchCanvas"></canvas>
                    </div>
                    <div id="caseContent" style="display: none;">
                        <!-- 案例内容将在检索完成后显示 -->
                    </div>
                </div>
            `;
        }

        // 加载法律分析第二部分（显示案例检索动画）
        function loadLegalAnalysisSecondPart() {
            const secondPart = document.getElementById('secondPartContent');
            if (!secondPart) return;

            // 显示第二部分容器和案例检索动画
            secondPart.style.display = 'block';
            secondPart.style.animation = 'fadeIn 0.8s ease';

            // 开始案例检索动画
            startCaseSearchAnimation();

            // 3秒后显示案例内容
            setTimeout(() => {
                loadCaseAnalysisContent();
            }, 3000);
        }

        // 案例检索动画
        function startCaseSearchAnimation() {
            const canvas = document.getElementById('caseSearchCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 创建案例相关关键词节点
            const caseKeywords = ['产品责任纠纷', '网络购物合同', '消费者胜诉', '举证标准', '三包义务', '质量缺陷'];
            const nodes = [];

            for (let i = 0; i < caseKeywords.length; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 1.2,
                    vy: (Math.random() - 0.5) * 1.2,
                    radius: Math.random() * 4 + 5,
                    keyword: caseKeywords[i],
                    alpha: Math.random() * 0.5 + 0.5,
                    color: `hsl(${30 + Math.random() * 40}, 75%, 65%)` // 橙色系，区别于法条的蓝色系
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.0025 + node.x * 0.008) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = node.color.replace(')', `, ${node.alpha})`).replace('hsl', 'hsla');
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '11px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 8);
                });

                // 绘制连接线
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        const dx = nodes[j].x - nodes[i].x;
                        const dy = nodes[j].y - nodes[i].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 140) {
                            const opacity = (140 - distance) / 140 * 0.25;
                            ctx.strokeStyle = `rgba(245, 166, 35, ${opacity})`;
                            ctx.lineWidth = 1.5;
                            ctx.beginPath();
                            ctx.moveTo(nodes[i].x, nodes[i].y);
                            ctx.lineTo(nodes[j].x, nodes[j].y);
                            ctx.stroke();
                        }
                    }
                }

                requestAnimationFrame(animate);
            }

            animate();
        }

        // 加载案例分析内容
        function loadCaseAnalysisContent() {
            const caseSearchAnimation = document.getElementById('caseSearchAnimation');
            const caseContent = document.getElementById('caseContent');
            const nextButton = document.getElementById('viewCourtSimulationBtn');

            if (caseSearchAnimation) {
                caseSearchAnimation.style.display = 'none';
            }

            if (caseContent) {
                caseContent.innerHTML = `
                    <div style="animation: fadeIn 0.8s ease;">
                        <h4>指导案例</h4>
                        <div class="case-item">
                            <h5>刘某诉深圳市星辰科技有限公司买卖合同纠纷案</h5>
                            <p><strong>案号：</strong>（2024）粤0305民初8756号</p>
                            <p><strong>基本案情：</strong>原告刘某于2024年3月15日通过被告官方网站购买"星辰游戏本Pro"笔记本电脑，支付价款13,999元。产品于3月18日送达，使用20天后频繁出现蓝屏死机、CPU过热降频、显卡驱动崩溃等故障，严重影响正常使用。原告多次通过在线客服、电话热线联系被告要求退货，被告以"可能系软件兼容性问题"、"需要进一步检测"等理由拒绝退货，仅同意提供维修服务。</p>
                            <p><strong>争议焦点：</strong>①涉案产品故障是否属于质量缺陷；②在无第三方检测报告情况下，消费者举证责任如何分配；③网络购物中经营者的质量保证义务范围。</p>
                            <p><strong>法院认定：</strong>被告作为专业电脑销售商，对产品质量承担更高注意义务。原告提供的故障录屏视频、系统错误日志、客服聊天记录等证据相互印证，形成完整证据链，足以证明产品存在质量缺陷。被告未能提供充分证据证明故障系原告使用不当所致，应承担举证不能的不利后果。</p>
                            <p><strong>判决要点：</strong>①支持原告退货退款请求，被告退还货款13,999元；②驳回被告关于"需要专业检测"的抗辩；③诉讼费由被告承担。</p>
                            <p><strong>典型意义：</strong>本案明确了网购电子产品质量纠纷中的举证责任分配原则，消费者只需证明产品存在故障及其与正常使用的因果关系，经营者需证明故障非产品质量问题。</p>
                        </div>

                        <div class="case-item">
                            <h5>陈某诉北京智能设备有限公司产品责任纠纷案</h5>
                            <p><strong>案号：</strong>（2023）京0108民初15432号</p>
                            <p><strong>基本案情：</strong>原告陈某2023年8月在被告天猫旗舰店购买"智能办公本X1"笔记本电脑，价款11,888元。收货后第15天开始出现间歇性黑屏、键盘部分按键失灵、散热风扇异响等问题。被告客服要求原告自费邮寄至指定维修点检测，原告认为不合理并要求直接退货。双方协商未果，原告诉至法院。</p>
                            <p><strong>关键证据：</strong>①天猫订单详情及支付宝付款记录；②故障现象手机录像12段，时长共计8分钟；③与客服沟通的旺旺聊天记录截图23张；④第三方电脑维修店出具的初步检测意见书。</p>
                            <p><strong>法院观点：</strong>网络购物环境下，消费者无法在购买前充分了解产品实际状况，经营者应承担更严格的质量保证义务。消费者在"三包"期内发现质量问题，有权在退货、换货、修理中自主选择救济方式。经营者不得以"需要专业检测"、"可能人为损坏"等理由单方面限制消费者的选择权。</p>
                            <p><strong>判决结果：</strong>①被告退还货款11,888元；②赔偿原告因处理此事产生的交通费、通讯费、误工费等合理损失1,200元；③案件受理费由被告负担。</p>
                            <p><strong>指导价值：</strong>确立了消费者在产品质量争议中享有救济方式选择权，经营者应主动承担质量保证责任，不得通过设置检测门槛等方式规避法定义务。</p>
                        </div>

                        <h4>法律分析结论</h4>
                        <div class="report-highlight">
                            <p><strong>胜诉概率评估：</strong>基于现有证据和相关法律条文，本案胜诉概率约为75-85%。</p>
                            <p><strong>关键法律依据：</strong>消费者权益保护法第二十四条为主要依据，民法典违约责任条款为补充。</p>
                            <p><strong>证据补强建议：</strong>建议尽快获取第三方质检报告，将胜诉概率提升至90%以上。</p>
                        </div>
                    </div>
                `;

                caseContent.style.display = 'block';

                // 法律分析完成后显示"查看模拟法庭推演"按钮
                if (nextButton) {
                    nextButton.style.display = 'block';
                    nextButton.style.animation = 'fadeIn 0.5s ease';
                }
            }
        }
        // 加载模拟法庭内容
        function loadCourtSimulationContent() {
            const container = document.getElementById('courtSimulationContent');
            if (!container) return;

            container.innerHTML = `
                <div class="court-simulation-container">
                    <div class="court-header">
                        <div class="court-title">
                            <h4>🏛️ 模拟法庭辩论现场</h4>
                            <p class="court-subtitle">案件：张三诉未来科技电脑有限公司买卖合同纠纷案</p>
                        </div>
                        <div class="court-status">
                            <span class="status-indicator">● 庭审进行中</span>
                        </div>
                    </div>

                    <div class="court-participants">
                        <div class="participant plaintiff">
                            <div class="avatar">👨‍💼</div>
                            <span>原告方</span>
                        </div>
                        <div class="participant judge">
                            <div class="avatar">⚖️</div>
                            <span>审判长</span>
                        </div>
                        <div class="participant defendant">
                            <div class="avatar">🏢</div>
                            <span>被告方</span>
                        </div>
                    </div>

                    <div id="courtDialogue" class="court-dialogue">
                        <!-- 对话内容将动态加载 -->
                    </div>

                    <div class="court-controls">
                        <button id="startCourtDebate" class="court-btn primary">开始庭审辩论</button>
                        <button id="pauseCourtDebate" class="court-btn secondary" style="display: none;">暂停</button>
                        <button id="skipCourtDebate" class="court-btn secondary" style="display: none;">中止</button>
                    </div>

                    <div id="courtSummary" class="court-summary" style="display: none;">
                        <h4>庭审总结</h4>
                        <div class="summary-content">
                            <div class="summary-section">
                                <h5>🎯 争议焦点</h5>
                                <ul>
                                    <li>产品故障是否属于质量缺陷</li>
                                    <li>商家是否履行了三包义务</li>
                                    <li>误工损失的计算标准和因果关系</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h5>💡 论证建议</h5>
                                <ul>
                                    <li>尽快获取第三方检测报告，强化产品质量缺陷证明</li>
                                    <li>收集更多商家推诿的证据材料，如录音、聊天记录</li>
                                    <li>完善误工损失的计算依据和相关证明文件</li>
                                    <li>准备同类产品的质量标准对比材料</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h5>📊 论证强度</h5>
                                <div class="probability-bar">
                                    <div class="probability-fill" style="width: 75%"></div>
                                    <span class="probability-text">75%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 绑定庭审控制按钮事件
            setTimeout(() => {
                const startBtn = document.getElementById('startCourtDebate');
                const pauseBtn = document.getElementById('pauseCourtDebate');
                const skipBtn = document.getElementById('skipCourtDebate');

                if (startBtn) {
                    startBtn.addEventListener('click', startCourtDebateAnimation);
                }
                if (pauseBtn) {
                    pauseBtn.addEventListener('click', pauseCourtDebateAnimation);
                }
                if (skipBtn) {
                    skipBtn.addEventListener('click', skipCourtDebateAnimation);
                }
            }, 100);
        }

        // 模拟法庭辩论数据
        const courtDebateData = [
            {
                speaker: 'judge',
                name: '审判长',
                content: '现在开庭。今天审理张三诉未来科技电脑有限公司买卖合同纠纷一案。根据《民事诉讼法》相关规定，本庭将依法公正审理此案。请原告代理人详细陈述案件事实经过、争议焦点以及具体的诉讼请求，并提供相关证据材料。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '尊敬的审判长，我方当事人张三于2024年6月15日在被告未来科技电脑有限公司官方旗舰店购买了一台高端游戏电脑，价值12,888元，配置包括最新处理器和独立显卡。然而该产品在使用仅两周后就出现了严重的性能故障，包括频繁蓝屏死机、CPU过热保护、显卡驱动异常等问题，严重影响正常使用。我方多次通过电话、邮件、在线客服等方式联系被告要求退货退款，但被告方以各种理由推诿拖延，拒绝履行《消费者权益保护法》规定的法定义务，给我方造成了经济损失和精神困扰。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '审判长，我方认为原告的指控缺乏充分的事实和法律依据。首先，产品出现的所谓故障很可能是由于原告在使用过程中自行安装了不兼容的第三方软件、游戏外挂程序，或者因为病毒感染、操作不当等人为因素导致的系统不稳定，并非我方产品本身存在质量缺陷。其次，我方严格按照国家三包规定和售后服务承诺，为原告提供了多次远程技术支持和系统重装服务，已经尽到了应有的售后服务义务。原告要求退货的理由不充分，不符合法定退货条件。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '被告方的抗辩理由完全站不住脚，存在明显的事实认定错误和法律适用错误。根据《消费者权益保护法》第二十四条明确规定，经营者提供的商品不符合质量要求的，消费者可以依照国家规定、当事人约定退货，或者要求经营者履行更换、修理等义务。我方提供的证据包括：购买发票和合同、产品故障现象的完整录像资料、与客服人员的聊天记录截图、技术人员上门检测的工作单据等，这些证据形成了完整的证据链，足以证明产品确实存在质量问题，而非人为因素造成。被告方应当承担举证责任证明产品不存在质量缺陷。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '请被告方针对原告提供的具体证据材料发表详细意见，特别是对于产品故障录像、客服聊天记录以及技术检测报告等关键证据的真实性、合法性和关联性进行说明。同时，请说明贵方是否有相反证据证明产品不存在质量问题。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '审判长，我方认为原告提供的证据存在以下问题：第一，故障录像虽然显示了产品出现问题的现象，但无法证明这些问题是由产品本身的质量缺陷造成的，而不是外部因素导致的。第二，客服聊天记录只能证明双方进行了沟通，但不能证明我方拒绝提供合理的售后服务。第三，原告所谓的技术检测报告并非来自具有相应资质的权威第三方检测机构，不具备法律效力。根据《产品质量法》相关规定，产品质量争议应当以具有法定资质的检测机构出具的检测报告为准。我方建议原告提供权威机构的产品质量检测报告，否则其主张缺乏充分的证据支持。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '被告方的观点存在明显的法律理解偏差。根据最高人民法院《关于民事诉讼证据的若干规定》以及相关司法解释，在消费者权益保护纠纷中，消费者只需要提供形成优势证据的证明材料即可，无需承担过重的举证责任。我方提供的证据已经形成了完整的证据链条：购买关系真实明确、产品故障现象客观存在、商家推诿拖延有据可查、因此造成的损失有理有据。这些证据相互印证，足以证明被告存在违约行为。而且，根据《消费者权益保护法》的立法精神，应当更多地保护消费者的合法权益，被告作为专业的经营者，应当承担更重的举证责任。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '现在请双方就原告主张的误工损失赔偿问题进行辩论。原告方请详细说明误工损失的计算依据、具体数额以及与本案争议事实之间的因果关系。被告方可以就此发表反驳意见。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '关于误工损失的问题，我方当事人张三是一名软件工程师，月薪8000元，因为处理这起消费纠纷，需要多次请假与被告方进行协商沟通、收集整理证据材料、咨询法律专业人士等，总共耽误了5个工作日，按照日均工资计算，误工损失为2000元。我方提供了劳动合同、工资流水、请假条等证明材料，这些损失都是被告违约行为的直接后果，具有明确的因果关系。根据《合同法》和《消费者权益保护法》的相关规定，被告应当赔偿因其违约行为给消费者造成的合理损失，包括直接损失和间接损失。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '我方认为原告主张的误工损失缺乏合理性和必要性。首先，原告所谓的误工损失与产品质量问题之间不存在直接的因果关系，即使产品确实存在问题，也不必然导致如此高额的误工损失。其次，原告提供的证据材料无法充分证明其确实因此遭受了2000元的经济损失，工资流水和请假条的真实性和关联性都存在疑问。第三，根据合同法的相关原理，损失赔偿应当遵循合理性和可预见性原则，原告主张的误工损失明显超出了合理范围，不应得到法院支持。我方建议法院驳回原告关于误工损失的诉讼请求。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '经过充分的法庭调查、举证质证和法庭辩论，本案的争议焦点已经非常明确，双方的观点和理由也都得到了充分的表达。本庭认为，双方当事人围绕产品质量问题、退货条件、损失赔偿等争议焦点进行了充分的举证和辩论。现在法庭调查和辩论环节已经结束，本庭将根据查明的事实，依照《消费者权益保护法》、《合同法》、《产品质量法》等相关法律法规的规定，以及最高人民法院相关司法解释的精神，对本案进行全面审理。现在宣布休庭，本庭将依法进行评议，并在法定期限内择日公开宣判。'
            }
        ];

        let currentMessageIndex = 0;
        let isDebateRunning = false;
        let debateInterval = null;

        // 开始庭审辩论动画
        function startCourtDebateAnimation() {
            if (isDebateRunning) return;

            isDebateRunning = true;
            currentMessageIndex = 0;

            const startBtn = document.getElementById('startCourtDebate');
            const pauseBtn = document.getElementById('pauseCourtDebate');
            const skipBtn = document.getElementById('skipCourtDebate');
            const dialogueContainer = document.getElementById('courtDialogue');

            // 更新按钮状态
            startBtn.style.display = 'none';
            pauseBtn.style.display = 'inline-block';
            skipBtn.style.display = 'inline-block';

            // 清空对话容器
            dialogueContainer.innerHTML = '';

            // 开始显示对话
            showNextMessage();
        }

        // 显示下一条消息
        function showNextMessage() {
            if (currentMessageIndex >= courtDebateData.length) {
                // 辩论结束，显示总结
                finishCourtDebate();
                return;
            }

            const message = courtDebateData[currentMessageIndex];
            const dialogueContainer = document.getElementById('courtDialogue');

            // 创建消息元素
            const messageElement = document.createElement('div');
            messageElement.className = `dialogue-message ${message.speaker}`;

            messageElement.innerHTML = `
                <div class="message-avatar">${getAvatarEmoji(message.speaker)}</div>
                <div class="message-bubble">
                    <div class="message-speaker">${message.name}</div>
                    <div class="message-content" id="message-${currentMessageIndex}">
                        <div class="loading-dots">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                </div>
            `;

            dialogueContainer.appendChild(messageElement);

            // 滚动到底部
            dialogueContainer.scrollTop = dialogueContainer.scrollHeight;

            // 随机延迟1-2秒后开始打字机效果
            const randomDelay = Math.random() * 1000 + 1000; // 1000-2000ms
            setTimeout(() => {
                if (!isDebateRunning) return;

                // 移除加载动画，开始打字机效果
                const contentElement = document.getElementById(`message-${currentMessageIndex}`);
                contentElement.innerHTML = '';
                contentElement.classList.add('typing-cursor');

                typeWriterEffect(contentElement, message.content, 30, () => {
                    // 移除打字机光标
                    contentElement.classList.remove('typing-cursor');

                    // 延迟后显示下一条消息
                    setTimeout(() => {
                        if (isDebateRunning) {
                            currentMessageIndex++;
                            showNextMessage();
                        }
                    }, 1500);
                });
            }, randomDelay);
        }

        // 获取头像表情符号
        function getAvatarEmoji(speaker) {
            switch(speaker) {
                case 'judge': return '⚖️';
                case 'plaintiff': return '👨‍💼';
                case 'defendant': return '🏢';
                default: return '👤';
            }
        }

        // 打字机效果
        function typeWriterEffect(element, text, speed, callback) {
            let i = 0;
            element.textContent = '';

            function type() {
                if (i < text.length && isDebateRunning) {
                    element.textContent += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else if (i >= text.length) {
                    if (callback) callback();
                }
            }

            type();
        }

        // 暂停/继续庭审辩论
        function pauseCourtDebateAnimation() {
            const pauseBtn = document.getElementById('pauseCourtDebate');

            if (isDebateRunning) {
                isDebateRunning = false;
                pauseBtn.textContent = '继续';
            } else {
                isDebateRunning = true;
                pauseBtn.textContent = '暂停';
                showNextMessage();
            }
        }

        // 中止庭审辩论
        function skipCourtDebateAnimation() {
            isDebateRunning = false;

            const dialogueContainer = document.getElementById('courtDialogue');

            // 添加中止提示消息
            const stopMessage = document.createElement('div');
            stopMessage.className = 'dialogue-message judge';
            stopMessage.innerHTML = `
                <div class="message-avatar">⚖️</div>
                <div class="message-bubble">
                    <div class="message-speaker">审判长</div>
                    <div class="message-content" style="color: #e74c3c; font-weight: 500;">庭审辩论已中止。</div>
                </div>
            `;
            dialogueContainer.appendChild(stopMessage);

            // 滚动到底部
            dialogueContainer.scrollTop = dialogueContainer.scrollHeight;

            // 更新按钮状态
            const startBtn = document.getElementById('startCourtDebate');
            const pauseBtn = document.getElementById('pauseCourtDebate');
            const skipBtn = document.getElementById('skipCourtDebate');

            startBtn.style.display = 'inline-block';
            startBtn.textContent = '重新开始';
            pauseBtn.style.display = 'none';
            skipBtn.style.display = 'none';
        }

        // 完成庭审辩论
        function finishCourtDebate() {
            isDebateRunning = false;

            const startBtn = document.getElementById('startCourtDebate');
            const pauseBtn = document.getElementById('pauseCourtDebate');
            const skipBtn = document.getElementById('skipCourtDebate');
            const summaryElement = document.getElementById('courtSummary');
            const nextButton = document.getElementById('viewLegalDocumentBtn');

            // 更新按钮状态
            startBtn.textContent = '重新开始';
            startBtn.style.display = 'inline-block';
            pauseBtn.style.display = 'none';
            skipBtn.style.display = 'none';

            // 显示总结
            summaryElement.style.display = 'block';
            summaryElement.scrollIntoView({ behavior: 'smooth' });

            // 动画显示论证强度
            setTimeout(() => {
                const probabilityFill = document.querySelector('.probability-fill');
                if (probabilityFill) {
                    probabilityFill.style.width = '75%';
                }

                // 辩论完成后显示"查看完整分析报告与起诉文书"按钮
                if (nextButton) {
                    nextButton.style.display = 'block';
                    nextButton.style.animation = 'fadeIn 0.5s ease';
                }
            }, 500);
        }
        // 加载起诉文书内容
        function loadLegalDocumentContent() {
            const container = document.getElementById('legalDocumentContent');
            if (!container) return;

            container.innerHTML = `
                <div class="document-content">
                    <div class="document-header">
                        <h3>民事起诉状</h3>
                    </div>

                    <div style="margin: 2rem 0;">
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>原告：</strong>张三，男，1995年10月20日生，汉族</p>
                            <p>身份证号：11010119951020XXXX</p>
                            <p>住址：北京市朝阳区建国路XX号院X号楼X单元XXX室</p>
                            <p>联系电话：138-0013-8000</p>
                        </div>
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>被告：</strong>未来科技电脑有限公司</p>
                            <p>统一社会信用代码：91440300MA5GXXXXXX</p>
                            <p>住所地：广东省深圳市南山区科技园路XX号</p>
                            <p>法定代表人：李四</p>
                            <p>联系电话：0755-88886666</p>
                        </div>
                        <div style="text-align: center; font-size: 1.1rem; font-weight: 500; margin: 2rem 0; padding: 1rem; background: rgba(179, 205, 224, 0.1); border-radius: 8px;">
                            <p><strong>案由：</strong>买卖合同纠纷</p>
                        </div>
                    </div>

                    <div style="margin: 2rem 0;">
                        <h4 style="color: #333; font-size: 1.2rem; font-weight: 500; margin-bottom: 1rem;">诉讼请求：</h4>
                        <ol style="padding-left: 2rem;">
                            <li style="margin: 0.5rem 0; line-height: 1.8;">一、判令被告立即退还原告购物款人民币12,888元；</li>
                            <li style="margin: 0.5rem 0; line-height: 1.8;">二、判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；</li>
                            <li style="margin: 0.5rem 0; line-height: 1.8;">三、判令被告承担本案全部诉讼费用。</li>
                        </ol>
                    </div>

                    <div style="margin: 2rem 0;">
                        <h4 style="color: #333; font-size: 1.2rem; font-weight: 500; margin-bottom: 1rem;">事实与理由：</h4>
                        <div style="line-height: 1.8; text-indent: 2em;">
                            <p><strong>一、双方买卖合同关系的确立</strong></p>
                            <p>2025年7月15日，原告通过被告官方网站购买了一台"幻影X Pro"笔记本电脑，订单号为880123456789，支付价款人民币12,888元。产品于2025年7月18日送达原告处，原告当场验收并签收。至此，双方买卖合同关系正式确立。</p>

                            <p><strong>二、被告交付的产品存在严重质量缺陷</strong></p>
                            <p>自2025年8月10日起，该笔记本电脑频繁出现以下故障：系统频繁蓝屏死机，每日发生3-5次；机身异常过热，温度超过正常使用范围；运行速度极慢，无法正常处理日常办公任务；多次自动关机，造成工作文件丢失。上述故障严重影响产品的正常使用，明显不符合同类产品应有的质量标准。</p>

                            <p><strong>三、被告拒绝履行三包义务</strong></p>
                            <p>原告发现产品质量问题后，立即通过客服热线、在线客服等多种方式联系被告，要求按照三包规定进行退换货处理。但被告客服人员以"软件兼容性问题"、"用户操作不当"等理由推诿责任，拒绝提供有效解决方案。</p>

                            <p><strong>四、法律依据</strong></p>
                            <p>根据《中华人民共和国消费者权益保护法》第二十四条规定，经营者提供的商品不符合质量要求的，消费者可以要求退货。根据《中华人民共和国民法典》第五百七十七条规定，当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担违约责任。</p>
                        </div>
                    </div>

                    <p style="margin: 2rem 0; line-height: 1.8;"><strong>综上所述，</strong>原告的诉讼请求事实清楚、理由充分、法律依据明确，请求人民法院依法支持原告的全部诉讼请求。</p>

                    <div style="text-align: right; margin-top: 3rem;">
                        <p>此致</p>
                        <p><strong>北京市朝阳区人民法院</strong></p>
                        <br>
                        <p>具状人：张三</p>
                        <p>2025年8月25日</p>
                    </div>

                    <div style="margin-top: 2rem; padding: 1rem; background: rgba(245, 166, 35, 0.1); border-radius: 8px;">
                        <h5 style="color: #F5A623; margin-bottom: 0.5rem;">附件清单：</h5>
                        <p>1. 网络购物订单截图及支付凭证</p>
                        <p>2. 产品故障现象录像及照片</p>
                        <p>3. 与客服沟通记录截图</p>
                        <p>4. 误工损失相关证明材料</p>
                        <p>5. 其他相关证据材料</p>
                    </div>
                </div>
            `;
        }



        // 下载完整报告功能
        function downloadReport() {
            log('下载完整报告');

            // 创建报告内容
            const reportContent = `法弈智能法律诉讼辅助系统 - 完整分析报告

案件概况：
- 案件类型：买卖合同纠纷
- 争议金额：12,888元
- 预估胜率：78%
- 证据完整度：80%

证据分析：
已有证据：网络购物订单、在线支付凭证、故障现象录像、客服沟通记录等4项
关键缺失：第三方质检报告1项

法律依据：
- 《中华人民共和国消费者权益保护法》第二十四条
- 《中华人民共和国民法典》第五百七十七条

策略建议：
1. 立即获取第三方检测报告
2. 固化证据材料
3. 完善损失证明

生成时间：${new Date().toLocaleString()}`;

            downloadTextFile(reportContent, '法弈分析报告.txt');
        }

        // 下载起诉文书功能
        function downloadDocument() {
            log('下载起诉文书');

            // 获取起诉状内容
            const documentContent = `民事起诉状

原告：张三，男，1995年10月20日出生，汉族，身份证号：11010119951020XXXX，住址：北京市朝阳区建国路XX号院X号楼X单元XXX室，联系电话：138-0013-8000。

被告：未来科技电脑有限公司，统一社会信用代码：91440300MA5GXXXXXX，住所地：广东省深圳市南山区科技园路XX号，法定代表人：李四，联系电话：0755-88886666。

案由：买卖合同纠纷

诉讼请求：
一、判令被告立即退还原告购物款人民币12,888元；
二、判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；
三、判令被告承担本案全部诉讼费用。

事实与理由：
（详细内容请参考完整文书）

此致
XX市XX区人民法院

起诉人：张三（手写签名）
二〇二五年八月二十四日

附项：
1. 本起诉状副本1份；
2. 证据材料复印件1套；
3. 原告身份证复印件1份。`;

            downloadTextFile(documentContent, '民事起诉状.txt');
        }

        // 通用文本文件下载函数
        function downloadTextFile(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成，开始初始化');

            // 绑定事件
            const startBtn = document.getElementById('startExperienceBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const viewReportBtn = document.getElementById('viewReportBtn');
            const viewLegalInsightsBtn = document.getElementById('viewLegalInsightsBtn');
            const viewCourtSimulationBtn = document.getElementById('viewCourtSimulationBtn');
            const viewLegalDocumentBtn = document.getElementById('viewLegalDocumentBtn');
            const backToEvidenceBtn = document.getElementById('backToEvidenceBtn');
            const backToLegalInsightsBtn = document.getElementById('backToLegalInsightsBtn');
            const backToCourtSimulationBtn = document.getElementById('backToCourtSimulationBtn');
            const downloadReportBtn = document.getElementById('downloadReportBtn');
            const downloadDocumentBtn = document.getElementById('downloadDocumentBtn');

            if (startBtn) {
                startBtn.addEventListener('click', switchToExperiencePage);
                log('立即体验按钮事件绑定成功');
            } else {
                log('错误：找不到立即体验按钮');
            }

            if (analyzeBtn) {
                analyzeBtn.addEventListener('click', startAnalysis);
                log('立即分析按钮事件绑定成功');
            }

            if (viewReportBtn) {
                viewReportBtn.addEventListener('click', showEvidenceChain);
                log('查看报告按钮事件绑定成功');
            }

            if (viewLegalInsightsBtn) {
                viewLegalInsightsBtn.addEventListener('click', showLegalInsights);
                log('查看法律分析按钮事件绑定成功');
            }

            if (viewCourtSimulationBtn) {
                viewCourtSimulationBtn.addEventListener('click', showCourtSimulation);
                log('查看模拟法庭按钮事件绑定成功');
            }

            if (viewLegalDocumentBtn) {
                viewLegalDocumentBtn.addEventListener('click', showLegalDocument);
                log('查看起诉文书按钮事件绑定成功');
            }

            if (backToEvidenceBtn) {
                backToEvidenceBtn.addEventListener('click', backToEvidenceChain);
                log('返回证据链分析按钮事件绑定成功');
            }

            if (backToLegalInsightsBtn) {
                backToLegalInsightsBtn.addEventListener('click', backToLegalInsights);
                log('返回法律分析按钮事件绑定成功');
            }

            if (backToCourtSimulationBtn) {
                backToCourtSimulationBtn.addEventListener('click', backToCourtSimulation);
                log('返回模拟法庭按钮事件绑定成功');
            }

            if (downloadReportBtn) {
                downloadReportBtn.addEventListener('click', downloadReport);
                log('下载报告按钮事件绑定成功');
            }

            if (downloadDocumentBtn) {
                downloadDocumentBtn.addEventListener('click', downloadDocument);
                log('下载文书按钮事件绑定成功');
            }

            log('初始化完成');
        });

        log('脚本加载完成');
    </script>
</body>
</html>
