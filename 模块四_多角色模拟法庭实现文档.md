# 模块四：多角色模拟法庭推演模块 (Multi-Role Court Simulation Module) 实现文档

## 1. 模块概述

### 1.1 功能定位
多角色模拟法庭推演模块是"法弈"系统的核心对抗与验证引擎。它的功能定位是接收来自**案件解构**、**证据策略**和**法律研究**模块的结构化输出，构建一个动态的、多智能体驱动的虚拟法庭环境。在此环境中，对我方（用户方）的全部论点、证据链、法律依据和整体策略进行全方位的“压力测试”，旨在模拟真实的庭审攻防，前瞻性地发现潜在风险、论证弱点和策略漏洞，并最终生成优化后的诉讼策略和风险应对预案。

### 1.2 核心职责
- **集成化环境构建**：基于模块一、二、三的输出，动态初始化原告、被告、法官等多方智能体，构建一个信息完备的模拟推演环境。
- **对抗式庭审模拟**：模拟庭审中的核心环节，如陈述、举证、质证和辩论。由“被告智能体”对我方证据链和论点发起针对性攻击，由“原告智能体”进行辩护。
- **智能化裁判评估**：由“法官智能体”依据法律研究成果和证据评估标准，对每一轮攻防的有效性进行实时评估和裁决，量化各方论证的强度。
- **策略优化与风险预警**：汇总整个推演过程的数据，精准识别我方策略的薄弱环节，并结合推演结果，生成具体的、可执行的论点优化建议、证据补强方案和风险应对策略。

## 2. 数据结构设计

### 2.1 输入数据结构

```python
class CourtSimulationInput:
    """模拟法庭输入"""
    def __init__(self):
        # (来自模块一) 包含案件所有结构化信息，是推演的“事实基础”
        self.hlkg: HLKG = None
        # (来自模块二) 我方核心的“弹药库”，包含证据清单、证据链和已知的证据缺口
        self.evidence_strategy: EvidenceStrategyOutput = None
        # (来自模块三) 庭审的“法律标尺”，包含相关法条和指导案例
        self.legal_research: LegalResearchOutput = None
        # 案件上下文，如当事人预设的性格、期望等
        self.case_context: Dict = {}
        # 模拟配置，如推演轮次、被告攻击性强度等
        self.simulation_config: Dict = {}
```

### 2.2 角色智能体结构

```python
class LegalAgent:
    """法律智能体基类"""
    def __init__(self, agent_id: str, knowledge_base: Dict):
        self.agent_id: str = agent_id                   # 智能体ID
        self.role_type: str = ""                        # 角色类型
        self.knowledge_base: Dict = knowledge_base      # 知识库 (来自输入模块)
        self.strategy_profile: Dict = {}                # 策略档案
        self.reasoning_engine: Any = None               # 推理引擎
        self.memory: List[Dict] = []                    # 记忆存储，记录庭审过程

class PlaintiffAgent(LegalAgent):
    """原告方智能体（我方）"""
    def __init__(self, agent_id: str, knowledge_base: Dict):
        super().__init__(agent_id, knowledge_base)
        self.role_type = "Plaintiff"
        # 从 evidence_strategy 中初始化诉讼资源
        self.claims: List[LegalClaimNode] = knowledge_base['hlkg'].get_nodes_by_type('LegalClaim')
        self.evidence_chains: List[EvidenceChain] = knowledge_base['evidence_strategy'].evidence_chains
        self.legal_basis: List[Dict] = knowledge_base['legal_research'].relevant_provisions

class DefendantAgent(LegalAgent):
    """被告方智能体（对手）"""
    def __init__(self, agent_id: str, knowledge_base: Dict):
        super().__init__(agent_id, knowledge_base)
        self.role_type = "Defendant"
        # 核心职责是攻击我方弱点，其策略基于我方的证据缺口报告
        self.attack_points: List[EvidenceGap] = knowledge_base['evidence_strategy'].gap_report.gaps
        self.defense_strategies: List[str] = self._develop_defense_strategies()

    def _develop_defense_strategies(self) -> List[str]:
        """基于我方弱点制定攻击策略"""
        strategies = []
        for gap in self.attack_points:
            if gap.gap_type == "证明力不足":
                strategies.append(f"攻击与'{gap.target_element}'相关的证据链强度")
            if gap.gap_type == "完全缺失":
                strategies.append(f"主张对方未能证明'{gap.target_element}'这一法律要件")
        # 还可以基于相似案例中对方的抗辩理由来构建策略
        for case in self.knowledge_base['legal_research'].similar_cases:
            if case.get('judgment_result') == 'plaintiff_loses':
                 strategies.append(f"参考案例'{case.case_name}'中的抗辩逻辑")
        return strategies

class JudgeAgent(LegalAgent):
    """法官智能体（中立评估者）"""
    def __init__(self, agent_id: str, knowledge_base: Dict):
        super().__init__(agent_id, knowledge_base)
        self.role_type = "Judge"
        # 法官的裁决依据来自于法律研究模块的结果
        self.legal_standards: List[Dict] = knowledge_base['legal_research'].relevant_provisions
        self.precedent_preferences: List[Dict] = knowledge_base['legal_research'].similar_cases
        self.judgment_criteria: Dict = self._build_judgment_criteria()

    def _build_judgment_criteria(self) -> Dict:
        """构建判决标准"""
        criteria = {}
        # 从HLKG中提取所有需要证明的法律要件
        legal_elements = self.knowledge_base['hlkg'].get_nodes_by_type('LegalElement')
        for element in legal_elements:
            criteria[element.properties['name']] = {'proven': False, 'strength': 0.0}
        return criteria
```

### 2.3 推演过程结构

```python
class ArgumentExchange:
    """论证交锋"""
    def __init__(self):
        self.exchange_id: str = ""                      # 交锋ID
        self.topic: str = ""                            # 争议焦点 (通常是一个法律要件)
        self.plaintiff_argument: Dict = {}              # 原告论证 (含主张、证据链、法律依据)
        self.defendant_counter: Dict = {}               # 被告反驳 (含反驳点、质疑的证据)
        self.judge_evaluation: Dict = {}                # 法官评价 (含评估分数、评价理由)
        self.outcome: str = ""                          # 交锋结果 (favorable / unfavorable / neutral)
        self.impact_score: float = 0.0                  # 对案件整体走向的影响分数

class SimulationSession:
    """推演会话"""
    def __init__(self):
        self.session_id: str = ""                       # 会话ID
        self.participants: Dict[str, LegalAgent] = {}   # 参与者
        self.argument_exchanges: List[ArgumentExchange] = [] # 论证交锋记录
        self.session_state: Dict = {}                   # 会话状态 (记录各要件的证明情况)
        self.performance_metrics: Dict = {}             # 我方表现指标
```

### 2.4 输出数据结构

```python
class CourtSimulationOutput:
    """模拟法庭输出"""
    def __init__(self):
        # 针对在模拟中暴露弱点的原始论证，提出的具体优化版本
        self.optimized_arguments: List[Dict] = []
        # 识别出的核心风险点，以及被告最可能采用的攻击策略
        self.risk_assessment: Dict[str, str] = {}
        # 对我方证据链、法律依据的详细弱点分析报告
        self.weakness_analysis: Dict = {}
        # 针对风险和弱点，提出的具体应对策略
        self.counter_strategies: List[Dict] = []
        # 基于推演结果，对证据策略模块的反馈，如建议补充哪些证据
        self.evidence_optimization: Dict = {}
        # 最终的、总结性的策略建议
        self.final_recommendations: List[str] = []
        # 完整的推演过程报告，用于复盘
        self.simulation_report: SimulationSession = None
```

## 3. 核心算法设计

### 3.1 对抗式推演算法

```python
class AdversarialSimulator:
    """对抗式推演器"""

    def __init__(self, max_rounds: int = 10):
        self.max_rounds = max_rounds
        self.argument_evaluator = ArgumentStrengthEvaluator() # 论证强度评估器

    def run_adversarial_simulation(self, plaintiff_agent: PlaintiffAgent,
                                 defendant_agent: DefendantAgent,
                                 judge_agent: JudgeAgent) -> SimulationSession:
        """
        对抗式推演主算法

        算法流程:
        1. 初始化推演会话(Session)，确定所有需要辩论的争议焦点（法律要件）。
        2. 按顺序遍历每一个争议焦点，进行多轮对抗。
        3. 在每一轮中：
            a. **原告举证**：原告智能体根据当前争议焦点，从其知识库（来自模块二的`evidence_chains`）中选择最强的证据链，构建论证。
            b. **被告反驳**：被告智能体分析原告的论证，并依据其攻击策略（来自模块二的`gap_report`）对证据链的薄弱环节（如孤证、证明力不足）发起攻击。
            c. **法官评估**：法官智能体调用`ArgumentStrengthEvaluator`，结合法律依据（来自模块三）和证据情况，对双方的交锋进行评估，并给出结果。
        4. 实时更新会话状态，记录每个争议焦点的证明强度。
        5. 所有焦点辩论结束后，生成最终分析报告。
        """
        session = SimulationSession()
        session.participants = {"plaintiff": plaintiff_agent, "defendant": defendant_agent, "judge": judge_agent}
        session.session_state = judge_agent.judgment_criteria.copy()

        disputed_issues = list(session.session_state.keys())

        for issue in disputed_issues:
            exchange = self._execute_argument_exchange(session, issue)
            session.argument_exchanges.append(exchange)

            # 更新会话状态
            if exchange.outcome == 'favorable':
                session.session_state[issue]['proven'] = True
            session.session_state[issue]['strength'] = exchange.judge_evaluation.get('overall_strength', 0.0)

        return session

    def _execute_argument_exchange(self, session: SimulationSession, issue: str) -> ArgumentExchange:
        """执行单轮论证交锋"""
        exchange = ArgumentExchange()
        exchange.topic = issue
        plaintiff = session.participants['plaintiff']
        defendant = session.participants['defendant']
        judge = session.participants['judge']

        # a. 原告方构建论证
        plaintiff_arg, used_chain = self._generate_plaintiff_argument(plaintiff, issue)
        exchange.plaintiff_argument = plaintiff_arg

        # b. 被告方生成反驳
        defendant_counter = self._generate_defendant_counter(defendant, plaintiff_arg, used_chain)
        exchange.defendant_counter = defendant_counter

        # c. 法官进行评估
        judge_eval = self.argument_evaluator.evaluate_exchange(
            plaintiff_arg, defendant_counter, judge.knowledge_base
        )
        exchange.judge_evaluation = judge_eval
        exchange.outcome = "favorable" if judge_eval['overall_strength'] > 0.6 else "unfavorable"

        return exchange

    def _generate_plaintiff_argument(self, agent: PlaintiffAgent, issue: str) -> (Dict, EvidenceChain):
        """原告根据证据链构建论证"""
        # 找到与当前争议焦点最相关的证据链
        best_chain = None
        for chain in agent.evidence_chains:
            if chain.target_element == issue:
                if best_chain is None or chain.chain_strength > best_chain.chain_strength:
                    best_chain = chain
        if not best_chain:
            return {"argument": "未能提供有效证据", "evidence": [], "legal_basis": []}, None

        argument = {
            "argument": f"为证明'{issue}', 我方提供以下证据链: {best_chain.logical_flow}",
            "evidence_ids": best_chain.primary_evidence + best_chain.supporting_evidence,
            "legal_basis_ids": [p['provision_id'] for p in agent.legal_basis]
        }
        return argument, best_chain

    def _generate_defendant_counter(self, agent: DefendantAgent, plaintiff_arg: Dict, evidence_chain: EvidenceChain) -> Dict:
        """被告寻找我方弱点进行反驳"""
        # 优先从预设的攻击点（证据缺口）中寻找策略
        for weakness in evidence_chain.weak_points:
            return {"counter_argument": f"对方在'{evidence_chain.target_element}'上的证据存在明显弱点: {weakness['description']}"}

        # 如果证据链本身无明显弱点，则攻击证据本身的属性
        if len(plaintiff_arg['evidence_ids']) == 1:
            return {"counter_argument": "对方仅提供孤证，证明力不足，请求不予采信。"}

        return {"counter_argument": "被告对原告提供的证据的真实性、合法性、关联性均不予认可。"}

```

### 3.2 论证强度评估算法

```python
class ArgumentStrengthEvaluator:
    """论证强度评估器"""
    def evaluate_exchange(self, plaintiff_arg: Dict, defendant_counter: Dict, knowledge_base: Dict) -> Dict:
        """评估一次完整的论证交锋"""
        # 从知识库中获取评估所需信息
        evidence_strategy = knowledge_base['evidence_strategy']
        legal_research = knowledge_base['legal_research']

        # 1. 评估原告论证的内在强度
        evidence_support_score = self._evaluate_evidence_support(
            plaintiff_arg['evidence_ids'], evidence_strategy.core_evidence_list
        )
        legal_foundation_score = self._evaluate_legal_foundation(
            plaintiff_arg['legal_basis_ids'], legal_research.relevant_provisions
        )
        intrinsic_strength = 0.6 * evidence_support_score + 0.4 * legal_foundation_score

        # 2. 评估被告反驳的有效性
        counter_effectiveness = self._evaluate_counter_effectiveness(
            defendant_counter, plaintiff_arg, evidence_strategy
        )

        # 3. 计算最终综合强度
        overall_strength = intrinsic_strength * (1 - counter_effectiveness)

        return {
            'evidence_support': evidence_support_score,
            'legal_foundation': legal_foundation_score,
            'counter_effectiveness': counter_effectiveness,
            'overall_strength': overall_strength,
            'reason': f"原告论证基础分为{intrinsic_strength:.2f}, 遭遇被告有效性为{counter_effectiveness:.2f}的反驳。"
        }

    def _evaluate_evidence_support(self, evidence_ids: List[str], all_evidence: List[EvidenceAssessment]) -> float:
        """评估证据支撑度，直接利用模块二的评估结果"""
        if not evidence_ids: return 0.0
        total_probative_value = 0.0
        for evid in all_evidence:
            if evid.evidence_id in evidence_ids:
                total_probative_value += evid.probative_value
        # 考虑证据协同效应，多个证据 > 单个证据
        synergy_bonus = 0.1 if len(evidence_ids) > 1 else 0.0
        return min(total_probative_value / len(evidence_ids) + synergy_bonus, 1.0)

    def _evaluate_legal_foundation(self, basis_ids: List[str], all_provisions: List[Dict]) -> float:
        """评估法理依据性"""
        if not basis_ids or not all_provisions: return 0.5 # 默认中性
        # 此处可以简化为检查是否提供了法律依据
        return 1.0 if basis_ids else 0.2

    def _evaluate_counter_effectiveness(self, counter: Dict, plaintiff_arg: Dict, evidence_strategy: EvidenceStrategyOutput) -> float:
        """评估反驳的有效性"""
        if "孤证" in counter.get('counter_argument', ''):
            return 0.4 # 攻击孤证非常有效
        # 检查反驳是否命中了已知的证据缺口
        for gap in evidence_strategy.gap_report.gaps:
             if gap.target_element in counter.get('counter_argument', ''):
                 return 0.7 # 击中已知要害，非常有效
        return 0.1 # 常规性反驳，效果一般```

### 3.3 策略优化算法

```python
class StrategyOptimizer:
    """策略优化器"""
    def optimize_litigation_strategy(self, simulation_results: SimulationSession) -> CourtSimulationOutput:
        """
        基于模拟推演结果，生成最终的优化策略报告。

        算法流程:
        1. **识别核心弱点**: 遍历`simulation_results.argument_exchanges`，找出所有`outcome`为"unfavorable"的交锋，这些是我方策略的核心弱点。
        2. **分析失败原因**: 对于每个弱点，深入分析`judge_evaluation`和`defendant_counter`，确定失败是源于证据不足、法律引用不当还是对方攻击策略有效。
        3. **生成优化论证**: 如果问题出在论证本身，尝试重新组织证据或寻找补充证据的建议。
        4. **制定应对策略**: 提炼被告方的有效攻击模式，并为其制定应对预案。
        5. **汇总风险评估**: 将所有识别出的弱点和可能的对方策略汇总，形成最终的风险评估报告。
        """
        output = CourtSimulationOutput()
        output.simulation_report = simulation_results
        weaknesses = []
        risks = {}

        for exchange in simulation_results.argument_exchanges:
            if exchange.outcome == "unfavorable":
                weakness = {
                    'topic': exchange.topic,
                    'reason': exchange.judge_evaluation.get('reason'),
                    'defendant_tactic': exchange.defendant_counter.get('counter_argument')
                }
                weaknesses.append(weakness)
                risks[exchange.topic] = f"在该争议焦点上，我方论证强度不足，易被对方以'{exchange.defendant_counter.get('counter_argument')}'的策略攻击。"

        output.weakness_analysis = {"identified_weaknesses": weaknesses}
        output.risk_assessment = risks
        output.optimized_arguments, output.evidence_optimization = self._generate_improvement_suggestions(weaknesses, simulation_results.participants['plaintiff'].knowledge_base)
        output.final_recommendations = self._generate_final_recommendations(output)

        return output

    def _generate_improvement_suggestions(self, weaknesses: List[Dict], knowledge_base: Dict) -> (List[Dict], Dict):
        """生成改进建议"""
        optimized_args = []
        evidence_opt = {"suggestions": []}
        gap_report = knowledge_base['evidence_strategy'].gap_report

        for weakness in weaknesses:
            topic = weakness['topic']
            # 检查是否与已知证据缺口有关
            for gap in gap_report.gaps:
                if gap.target_element == topic:
                    suggestion = f"针对'{topic}'的弱点，急需补充以下证据来弥补缺口: {', '.join(gap.suggested_evidence)}"
                    evidence_opt['suggestions'].append(suggestion)
                    break

        # 此处可扩展更复杂的论证优化逻辑
        return optimized_args, evidence_opt

    def _generate_final_recommendations(self, output: CourtSimulationOutput) -> List[str]:
        """生成最终建议"""
        recs = ["### 最终策略建议 ###"]
        if not output.risk_assessment:
            recs.append("1. 整体策略稳健：在本次模拟推演中，我方核心论点均得到有效支持，未发现重大风险。")
        else:
            recs.append("1. 关注核心风险：请重点关注以下争议焦点的风险：")
            for topic, risk_desc in output.risk_assessment.items():
                recs.append(f"   - **{topic}**: {risk_desc}")
        if output.evidence_optimization['suggestions']:
            recs.append("2. 证据补强是关键：为应对上述风险，建议优先执行以下证据优化方案：")
            recs.extend([f"   - {s}" for s in output.evidence_optimization['suggestions']])
        return recs
```

## 4. 推演环境管理

### 4.1 环境配置
- **推演规则设定**：定义辩论的顺序、发言时间限制（如有）、证据提交规则等。
- **角色行为参数**：可配置被告方的攻击性等级（如“保守”、“激进”），法官的裁判偏好（如“偏重程序正义”、“偏重实质正义”）。
- **评估标准配置**：设定论证强度评估模型中的权重，以及判断“favorable”结果的阈值。
- **终止条件设置**：设定最大推演轮次，或当所有核心争议焦点都已辩论完毕后自动终止。

### 4.2 状态管理
- **推演状态跟踪**：实时记录`SimulationSession`的状态，特别是`session_state`中各法律要件的证明情况。
- **历史记录管理**：完整保存每一次`ArgumentExchange`的全部细节，便于复盘和分析。
- **实时性能监控**：监控推演过程的计算资源消耗和时间，确保系统稳定性。
- **异常处理机制**：处理因输入数据不完整或逻辑冲突导致的推演中断，并提供错误报告。

## 5. 质量保证机制

### 5.1 推演质量控制
- **论证逻辑验证**：确保原告方的论证严格基于模块二提供的证据链，避免凭空捏造。
- **角色行为一致性检查**：确保各智能体的行为符合其角色设定，例如被告方的攻击应主要围绕已知的证据缺口展开。
- **结果可信度评估**：通过比较不同配置下的多次模拟结果，评估输出建议的稳定性和可靠性。

### 5.2 优化效果验证
- **A/B测试框架**：允许用户使用原始策略和优化后策略分别进行模拟，直观对比两种策略在虚拟法庭中的表现差异。
- **效果评估指标**：定义一套量化指标来评估优化效果，如“不利交换轮次减少率”、“关键要件证明强度提升率”等。
- **反馈循环机制**：将本模块输出的`evidence_optimization`建议反馈给模块二，形成一个持续优化证据策略的闭环。

## 6. 接口设计

```python
class CourtSimulationModule:
    """模拟法庭推演模块主接口"""

    def run_simulation(self, input_data: CourtSimulationInput) -> CourtSimulationOutput:
        """
        运行模拟推演的统一入口。
        该方法将负责创建智能体、运行对抗模拟、并最终生成优化策略。
        """
        # 1. 创建法律智能体
        agents = self._create_legal_agents(input_data)

        # 2. 初始化并运行推演器
        simulator = AdversarialSimulator(max_rounds=input_data.simulation_config.get('max_rounds', 10))
        simulation_results = simulator.run_adversarial_simulation(
            agents['plaintiff'], agents['defendant'], agents['judge']
        )

        # 3. 初始化并运行策略优化器
        optimizer = StrategyOptimizer()
        final_output = optimizer.optimize_litigation_strategy(simulation_results)

        return final_output

    def _create_legal_agents(self, input_data: CourtSimulationInput) -> Dict[str, LegalAgent]:
        """私有方法：根据输入数据创建所有法律智能体"""
        knowledge_base = {
            "hlkg": input_data.hlkg,
            "evidence_strategy": input_data.evidence_strategy,
            "legal_research": input_data.legal_research
        }
        plaintiff = PlaintiffAgent('plaintiff_01', knowledge_base)
        defendant = DefendantAgent('defendant_01', knowledge_base)
        judge = JudgeAgent('judge_01', knowledge_base)
        return {"plaintiff": plaintiff, "defendant": defendant, "judge": judge}

    def generate_recommendations(self, simulation_output: CourtSimulationOutput) -> List[str]:
        """便捷接口：从完整的输出对象中提取最终的策略建议列表"""
        return simulation_output.final_recommendations```