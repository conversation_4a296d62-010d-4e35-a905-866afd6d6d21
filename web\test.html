<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度条测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            padding: 50px;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .analysis-text h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 500;
            text-align: center;
        }

        .analysis-stage {
            font-size: 1rem;
            color: #666;
            margin-bottom: 1.5rem;
            font-weight: 400;
            transition: color 0.3s ease;
            text-align: center;
        }

        .analysis-stage.completed {
            color: #28a745;
            font-weight: 500;
        }

        .progress-container {
            width: 100%;
            max-width: 400px;
            margin: 1.5rem auto;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(179, 205, 224, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #B3CDE0 0%, #A2D9CE 50%, #B3CDE0 100%);
            border-radius: 12px;
            width: 0%;
            transition: width 0.5s ease-out;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(179, 205, 224, 0.3);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .progress-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .progress-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #B3CDE0;
            animation: pulse 1.5s infinite;
        }

        .progress-dots span:nth-child(2) {
            animation-delay: 0.3s;
        }

        .progress-dots span:nth-child(3) {
            animation-delay: 0.6s;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .test-btn {
            display: block;
            margin: 30px auto;
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(179, 205, 224, 0.4);
        }

        .view-report-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
            display: none;
        }

        .view-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="analysis-text">
            <h3>模型分析进度</h3>
            <div id="analysisStage" class="analysis-stage">正在调用证据生成微调模型...</div>
            
            <!-- 进度条 -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">0%</div>
            </div>
            
            <div class="progress-dots">
                <span></span><span></span><span></span>
            </div>
        </div>
        
        <button id="testBtn" class="test-btn">开始测试进度条</button>
        <button id="viewReportBtn" class="view-report-btn">查看证据链分析报告</button>
    </div>

    <script>
        function startProgressAnimation() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const analysisStage = document.getElementById('analysisStage');
            const viewReportBtn = document.getElementById('viewReportBtn');

            // 重置状态
            progressFill.style.width = '0%';
            progressText.textContent = '0%';
            analysisStage.classList.remove('completed');
            viewReportBtn.style.display = 'none';

            const stages = [
                { progress: 0, text: '正在调用证据生成微调模型...', duration: 600 },
                { progress: 15, text: '正在解析案情描述...', duration: 900 },
                { progress: 35, text: '正在识别关键法律要素...', duration: 1000 },
                { progress: 55, text: '正在构建证据关系图...', duration: 1100 },
                { progress: 75, text: '正在分析证据完整性...', duration: 900 },
                { progress: 92, text: '正在生成分析报告...', duration: 700 },
                { progress: 100, text: '分析完成！', duration: 600 }
            ];

            let currentStage = 0;

            function updateStage() {
                if (currentStage < stages.length) {
                    const stage = stages[currentStage];
                    
                    // 更新进度条
                    progressFill.style.width = stage.progress + '%';
                    progressText.textContent = stage.progress + '%';
                    
                    // 更新阶段文本
                    analysisStage.textContent = stage.text;
                    
                    // 如果是最后一个阶段，添加完成样式
                    if (stage.progress === 100) {
                        analysisStage.classList.add('completed');
                    }
                    
                    console.log(`分析阶段 ${currentStage + 1}: ${stage.text} (${stage.progress}%)`);
                    
                    currentStage++;
                    
                    // 如果是最后一个阶段，显示查看报告按钮
                    if (currentStage === stages.length) {
                        setTimeout(() => {
                            viewReportBtn.style.display = 'block';
                            viewReportBtn.style.animation = 'fadeIn 0.5s ease';
                            console.log('查看报告按钮已显示');
                        }, stage.duration);
                    } else {
                        // 继续下一个阶段
                        setTimeout(updateStage, stage.duration);
                    }
                }
            }

            // 开始第一个阶段
            updateStage();
        }

        // 绑定测试按钮
        document.getElementById('testBtn').addEventListener('click', startProgressAnimation);
        
        document.getElementById('viewReportBtn').addEventListener('click', function() {
            alert('进度条测试完成！功能正常工作。');
        });
    </script>
</body>
</html>
